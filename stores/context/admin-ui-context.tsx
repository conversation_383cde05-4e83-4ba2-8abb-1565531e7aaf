// stores/context/admin-ui-context.tsx
import { createContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAdminUI } from '../use-admin-ui';

interface AdminUIContextProps {
  pathname: string;
  isEditorMode: boolean;
  setIsEditorMode: (isEnabled: boolean) => void;
  setCurrentSection: (section: string) => void;
  editorDevicePreview: 'desktop' | 'tablet' | 'mobile';
  editorIsPreviewMode: boolean;
  editorHasUnsavedChanges: boolean;
  editorCanUndo: boolean;
  editorCanRedo: boolean;
  setEditorDevicePreview: (device: 'desktop' | 'tablet' | 'mobile') => void;
  setEditorPreviewMode: (enabled: boolean) => void;
  setEditorHasUnsavedChanges: (hasChanges: boolean) => void;
  setEditorCanUndo: (canUndo: boolean) => void;
  setEditorCanRedo: (canRedo: boolean) => void;
}

const AdminUIContext = createContext<AdminUIContextProps>({} as AdminUIContextProps);

const AdminUIProvider = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();
  const {
    isEditorMode,
    setEditorMode,
    setCurrentSection,
    editorDevicePreview,
    editorIsPreviewMode,
    editorHasUnsavedChanges,
    editorCanUndo,
    editorCanRedo,
    setEditorDevicePreview,
    setEditorPreviewMode,
    setEditorHasUnsavedChanges,
    setEditorCanUndo,
    setEditorCanRedo,
    setSidebarCollapsed,
  } = useAdminUI();

  // Enhanced editor route detection
  const isPageBuilderRoute = pathname?.match(/^\/admin\/system\/pages\/[^\/]+$/);
  const isPageBuilderEditRoute = pathname?.match(/^\/admin\/system\/pages\/[^\/]+\/edit$/);
  const isLayoutBuilderRoute = pathname?.match(/^\/admin\/system\/layouts\/editor\/[^\/]+$/);
  const isUnifiedBuilderRoute = pathname?.match(/^\/admin\/system\/builders\/[^\/]+$/);
  const isAnyEditorRoute = isPageBuilderRoute || isPageBuilderEditRoute || isLayoutBuilderRoute || isUnifiedBuilderRoute;

  // Collapse sidebar on editor pages
  useEffect(() => {
    if (isAnyEditorRoute) {
      setSidebarCollapsed(true);
    }
  }, [isAnyEditorRoute, setSidebarCollapsed]);

  useEffect(() => {
    const pathParts = pathname.split('/');
    const section = pathParts[2]; // Assuming the section is the third part of the pathname (e.g., /admin/system/pages)

    setCurrentSection(section);
  }, [pathname, setCurrentSection]);

  return (
    <AdminUIContext.Provider 
      value={{
        pathname,
        isEditorMode,
        setIsEditorMode: setEditorMode,
        setCurrentSection,
        editorDevicePreview,
        editorIsPreviewMode,
        editorHasUnsavedChanges,
        editorCanUndo,
        editorCanRedo,
        setEditorDevicePreview,
        setEditorPreviewMode,
        setEditorHasUnsavedChanges,
        setEditorCanUndo,
        setEditorCanRedo,
      }}
    >
      {children}
    </AdminUIContext.Provider>
  );
};

export { AdminUIProvider, AdminUIContext };