import { create } from 'zustand'
import { persist } from 'zustand/middleware'

type Notification = {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  createdAt: Date
  read: boolean
}

type Breadcrumb = {
  label: string
  href: string
}

type EditorType = 'page' | 'layout' | 'unified' | null

type AdminUIState = {
  // State
  isSidebarCollapsed: boolean
  sidebarWidth: number
  notifications: Notification[]
  unreadNotificationsCount: number
  isDarkMode: boolean
  currentSection: string
  breadcrumbs: Breadcrumb[]
  // Editor mode
  isEditorMode: boolean
  currentEditorType: EditorType
  editorRightPanelOpen: boolean
  editorLeftPanelOpen: boolean
  editorLeftPanelWidth: number
  editorRightPanelWidth: number
  // Editor state
  editorHasUnsavedChanges: boolean
  editorCanUndo: boolean
  editorCanRedo: boolean
  editorIsPreviewMode: boolean
  editorDevicePreview: 'desktop' | 'tablet' | 'mobile'
}

type AdminUIActions = {
  toggleSidebar: () => void
  setSidebarWidth: (width: number) => void
  setSidebarCollapsed: (isCollapsed: boolean) => void
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => void
  markNotificationAsRead: (id: string) => void
  clearNotifications: () => void
  toggleTheme: () => void
  setCurrentSection: (section: string) => void
  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void
  // Editor actions
  setEditorMode: (isEnabled: boolean, editorType?: EditorType) => void
  toggleEditorMode: () => void
  toggleEditorRightPanel: () => void
  toggleEditorLeftPanel: () => void
  setEditorLeftPanelWidth: (width: number) => void
  setEditorRightPanelWidth: (width: number) => void
  // Editor state actions
  setEditorHasUnsavedChanges: (hasChanges: boolean) => void
  setEditorCanUndo: (canUndo: boolean) => void
  setEditorCanRedo: (canRedo: boolean) => void
  setEditorPreviewMode: (isPreview: boolean) => void
  setEditorDevicePreview: (device: 'desktop' | 'tablet' | 'mobile') => void
}

type AdminUIStore = AdminUIState & AdminUIActions

const initialState: AdminUIState = {
  isSidebarCollapsed: false,
  sidebarWidth: 280,
  notifications: [],
  unreadNotificationsCount: 0,
  isDarkMode: false,
  currentSection: 'dashboard',
  breadcrumbs: [{ label: 'Dashboard', href: '/admin' }],
  isEditorMode: false,
  currentEditorType: null,
  editorRightPanelOpen: true,
  editorLeftPanelOpen: true,
  editorLeftPanelWidth: 280,
  editorRightPanelWidth: 320,
  editorHasUnsavedChanges: false,
  editorCanUndo: false,
  editorCanRedo: false,
  editorIsPreviewMode: false,
  editorDevicePreview: 'desktop',
}

export const useAdminUI = create<AdminUIStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      toggleSidebar: () =>
        set((state) => ({ isSidebarCollapsed: !state.isSidebarCollapsed })),

      setSidebarCollapsed: (isCollapsed) =>
        set({ isSidebarCollapsed: isCollapsed }),

      setSidebarWidth: (width) =>
        set({ sidebarWidth: Math.max(200, Math.min(400, width)) }),

      addNotification: (notification) =>
        set((state) => {
          const newNotification = {
            ...notification,
            id: crypto.randomUUID(),
            createdAt: new Date(),
            read: false,
          }
          const notifications = [newNotification, ...state.notifications].slice(0, 100)
          return {
            notifications,
            unreadNotificationsCount: notifications.filter(n => !n.read).length,
          }
        }),

      markNotificationAsRead: (id) =>
        set((state) => {
          const notifications = state.notifications.map((n) =>
            n.id === id ? { ...n, read: true } : n
          )
          return {
            notifications,
            unreadNotificationsCount: notifications.filter((n) => !n.read).length,
          }
        }),

      clearNotifications: () =>
        set({ notifications: [], unreadNotificationsCount: 0 }),

      toggleTheme: () => 
        set((state) => ({ isDarkMode: !state.isDarkMode })),

      setCurrentSection: (section) =>
        set({ currentSection: section }),

      setBreadcrumbs: (breadcrumbs) =>
        set({ breadcrumbs }),

      // Editor actions
      setEditorMode: (isEnabled, editorType) =>
        set((state) => ({
          isEditorMode: isEnabled,
          currentEditorType: isEnabled ? editorType ?? state.currentEditorType : null,
          isSidebarCollapsed: isEnabled || state.isSidebarCollapsed,
        })),

      toggleEditorMode: () =>
        set((state) => ({
          isEditorMode: !state.isEditorMode,
          currentEditorType: !state.isEditorMode ? (state.currentEditorType || 'page') : null,
          isSidebarCollapsed: !state.isEditorMode || state.isSidebarCollapsed,
        })),

      toggleEditorRightPanel: () =>
        set((state) => ({ editorRightPanelOpen: !state.editorRightPanelOpen })),

      toggleEditorLeftPanel: () =>
        set((state) => ({ editorLeftPanelOpen: !state.editorLeftPanelOpen })),

      setEditorLeftPanelWidth: (width) =>
        set({ editorLeftPanelWidth: Math.max(200, Math.min(600, width)) }),

      setEditorRightPanelWidth: (width) =>
        set({ editorRightPanelWidth: Math.max(200, Math.min(600, width)) }),

      // Editor state actions
      setEditorHasUnsavedChanges: (hasChanges) =>
        set({ editorHasUnsavedChanges: hasChanges }),

      setEditorCanUndo: (canUndo) =>
        set({ editorCanUndo: canUndo }),

      setEditorCanRedo: (canRedo) =>
        set({ editorCanRedo: canRedo }),

      setEditorPreviewMode: (isPreview) =>
        set({ editorIsPreviewMode: isPreview }),

      setEditorDevicePreview: (device) =>
        set({ editorDevicePreview: device }),
    }),
    {
      name: 'admin-ui-storage',
      skipHydration: true,
      version: 1,
    }
  )
)