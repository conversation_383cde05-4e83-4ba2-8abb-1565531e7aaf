'use client'

import { useState, useMemo, useCallback } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Settings,
  Palette,
  Type,
  Zap,
  Database,
  Layout,
  RotateCcw,
  Eye,
  EyeOff,
  Search,
  Lightbulb,
  Copy,
  Save,
  History
} from 'lucide-react'
import { useSelectedComponent, useSelectedComponentProperties, useEditorStore } from '../stores/editor-store'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields/field-renderer'
import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { toast } from 'sonner'

const tabConfig = [
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Colors, spacing, borders, shadows',
    color: 'text-pink-400'
  },
  {
    id: 'content',
    label: 'Content',
    icon: Type,
    description: 'Text, images, links, media',
    color: 'text-blue-400'
  },
  {
    id: 'behavior',
    label: 'Behavior',
    icon: Zap,
    description: 'Animations, interactions, states',
    color: 'text-yellow-400'
  },
  {
    id: 'data',
    label: 'Data',
    icon: Database,
    description: 'Data sources, API connections',
    color: 'text-green-400'
  },
  {
    id: 'layout',
    label: 'Layout',
    icon: Layout,
    description: 'Positioning, sizing, alignment',
    color: 'text-purple-400'
  }
]

export function DarkPropertiesPanel() {
  const selectedComponent = useSelectedComponent()
  const propertyValues = useSelectedComponentProperties()
  const { updatePropertyValue, resetPropertyValues } = useEditorStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [activeTab, setActiveTab] = useState('appearance')

  const handlePropertyChange = useCallback((fieldId: string, value: any) => {
    if (selectedComponent) {
      updatePropertyValue(selectedComponent.id, fieldId, value)
    }
  }, [selectedComponent, updatePropertyValue])

  const handleResetProperties = useCallback(() => {
    if (selectedComponent) {
      resetPropertyValues(selectedComponent.id)
      toast.success('Properties reset to defaults')
    }
  }, [selectedComponent, resetPropertyValues])

  const renderFieldSection = useCallback((sectionId: string, fields: FieldConfig[]) => {
    if (!fields || fields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No {sectionId} properties available</div>
          <div className="text-xs mt-1">This component doesn't have configurable {sectionId} options</div>
        </div>
      )
    }

    const filteredFields = searchQuery
      ? fields.filter(field =>
          field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          field.id.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : fields

    if (filteredFields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <Search className="w-8 h-8 mx-auto mb-2 text-gray-600" />
          <div className="text-sm">No properties match "{searchQuery}"</div>
        </div>
      )
    }

    return (
      <div className="p-3 space-y-3">
        {filteredFields.map((field) => (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-300">
                {field.label}
              </label>
              {field.required && (
                <Badge variant="outline" className="text-xs border-red-400 text-red-400 bg-red-900/20">
                  Required
                </Badge>
              )}
            </div>
            
            {field.description && (
              <p className="text-xs text-gray-500">{field.description}</p>
            )}
            
            <div className="bg-gray-800 rounded-md p-2">
              <CustomFieldRenderer
                field={field}
                value={propertyValues?.[field.id] ?? field.defaultValue}
                onChange={(value) => handlePropertyChange(field.id, value)}
                className="dark-field"
              />
            </div>
          </div>
        ))}
      </div>
    )
  }, [searchQuery, propertyValues, handlePropertyChange])

  if (!selectedComponent) {
    return (
      <div className="h-full flex flex-col bg-gray-900">
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <EyeOff className="w-12 h-12 mx-auto mb-4 text-gray-600" />
            <div className="text-lg font-medium mb-2 text-gray-400">No Component Selected</div>
            <div className="text-sm text-gray-500">
              Select a component from the preview or generate one with AI to start editing properties
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Component Info */}
      <div className="p-3 border-b border-gray-700 bg-gray-800">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="font-medium text-gray-200">{selectedComponent.name}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetProperties}
              className="h-6 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <RotateCcw className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        <div className="text-xs text-gray-500 mb-2">{selectedComponent.description}</div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-500" />
          <Input
            placeholder="Search properties..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-7 h-7 bg-gray-700 border-gray-600 text-gray-300 placeholder-gray-500 text-xs"
          />
        </div>
      </div>

      {/* Properties Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <div className="border-b border-gray-700 bg-gray-800">
            <TabsList className="w-full grid grid-cols-5 h-10 bg-transparent">
              {tabConfig.map((tab) => (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center space-y-1 text-gray-400 data-[state=active]:text-white data-[state=active]:bg-gray-700 p-1"
                >
                  <tab.icon className={`w-3 h-3 ${tab.color}`} />
                  <span className="text-xs">{tab.label}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            {tabConfig.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
              >
                <ScrollArea className="flex-1">
                  {renderFieldSection(
                    tab.id,
                    selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                  )}
                </ScrollArea>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Smart Suggestions */}
      {showSuggestions && (
        <div className="border-t border-gray-700 bg-gray-800 p-3">
          <div className="flex items-center space-x-2 mb-2">
            <Lightbulb className="w-4 h-4 text-yellow-400" />
            <span className="text-sm font-medium text-gray-200">Smart Suggestions</span>
          </div>
          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start h-7 text-xs text-gray-400 hover:text-white hover:bg-gray-700"
            >
              Add hover animation
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start h-7 text-xs text-gray-400 hover:text-white hover:bg-gray-700"
            >
              Improve accessibility
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
