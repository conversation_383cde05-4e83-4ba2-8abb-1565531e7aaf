'use client'

import { useState } from 'react'
import { EditorLayout } from '@/components/admin/editor-layout'
import { AiChatPanel } from './ai-chat-panel'
import { PropertiesPanel } from './properties-panel'
import { LivePreview } from './live-preview'
import { ComponentTree } from './component-tree'
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Settings,
  Layers,
  MessageSquare,
  Sparkles,
  Download,
  Share,
  RotateCcw,
  Globe
} from 'lucide-react'
import { NextJSLayoutGenerator } from './nextjs-layout-generator'
import { useEditorStore } from '../stores/editor-store'
import { toast } from 'sonner'

export function AIVisualEditorLayout() {
  const {
    components,
    selectedComponentId,
    isGenerating,
    isAIResponding,
    clearChat
  } = useEditorStore()

  const selectedComponent = components.find(c => c.id === selectedComponentId)
  const [activeTab, setActiveTab] = useState('properties')

  const handleExportComponents = () => {
    if (components.length === 0) {
      toast.error('No components to export')
      return
    }

    const exportData = {
      components: components.map(comp => ({
        name: comp.name,
        description: comp.description,
        category: comp.category,
        jsx: comp.jsx,
        defaultValues: comp.defaultValues
      })),
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ai-components-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('Components exported successfully!')
  }

  const handleShareComponents = async () => {
    if (components.length === 0) {
      toast.error('No components to share')
      return
    }

    try {
      const shareData = {
        title: 'AI Generated Components',
        text: `Check out these ${components.length} AI-generated components!`,
        url: window.location.href
      }

      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      toast.error('Failed to share components')
    }
  }

  const handleResetEditor = () => {
    if (components.length === 0) {
      toast.info('Editor is already empty')
      return
    }

    if (confirm('This will delete all components and clear the chat. Are you sure?')) {
      clearChat()
      toast.success('Editor reset successfully')
    }
  }

  // Enhanced Left Panel - AI Chat
  const leftPanel = (
    <div className="h-full builder-panel builder-theme-visual">
      <div className="builder-panel-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-current" />
            <h3 className="font-semibold text-foreground">AI Assistant</h3>
          </div>
          <div className="flex items-center space-x-2">
            {(isGenerating || isAIResponding) && (
              <Badge variant="outline" className="text-current border-current/30 text-xs builder-pulse">
                <div className="w-2 h-2 bg-current rounded-full mr-1"></div>
                Working...
              </Badge>
            )}
            <div className="builder-help-indicator" data-tooltip="AI-powered component generation">
              ?
            </div>
          </div>
        </div>
      </div>
      <div className="h-[calc(100%-80px)] builder-slide-in">
        <AiChatPanel />
      </div>
    </div>
  )

  // Enhanced Right Panel - Properties and Components
  const rightPanel = (
    <div className="h-full builder-panel builder-theme-visual">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
        <div className="builder-panel-header">
          <TabsList className="w-full grid grid-cols-3 h-auto p-1 bg-muted/30">
            <TabsTrigger
              value="properties"
              className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              <Settings className="w-4 h-4" />
              <span>Properties</span>
            </TabsTrigger>
            <TabsTrigger
              value="components"
              className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              <Layers className="w-4 h-4" />
              <span>Components</span>
              {components.length > 0 && (
                <Badge variant="secondary" className="text-xs ml-1">
                  {components.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="nextjs"
              className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              <Globe className="w-4 h-4" />
              <span>Next.js</span>
              <Badge variant="outline" className="text-[10px] px-1 py-0 ml-1 builder-status-beta">
                Beta
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="properties" className="h-[calc(100%-80px)] mt-0 builder-slide-up">
          <PropertiesPanel />
        </TabsContent>

        <TabsContent value="components" className="h-[calc(100%-80px)] mt-0 builder-slide-up">
          <ComponentTree />
        </TabsContent>

        <TabsContent value="nextjs" className="h-[calc(100%-80px)] mt-0 builder-slide-up">
          <div className="builder-theme-nextjs h-full">
            <NextJSLayoutGenerator />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )

  // Enhanced Main Content - Live Preview with Header
  const mainContent = (
    <div className="h-full flex flex-col bg-background">
      {/* Enhanced Preview Header */}
      <div className="builder-panel-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-current/10 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-current" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">AI Visual Editor</h3>
                <p className="text-xs text-muted-foreground">Build components with AI assistance</p>
              </div>
            </div>

            <div className="flex items-center space-x-2 text-sm">
              <Badge variant="secondary" className="builder-status-ready">
                {components.length} component{components.length !== 1 ? 's' : ''}
              </Badge>
              {selectedComponent && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <Badge variant="outline" className="text-xs">
                    Editing: {selectedComponent.name}
                  </Badge>
                </>
              )}
              {(isGenerating || isAIResponding) && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <div className="flex items-center gap-1 text-current">
                    <div className="w-2 h-2 bg-current rounded-full builder-pulse"></div>
                    <span className="text-xs">AI Working</span>
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportComponents}
              disabled={components.length === 0}
              className="builder-button-secondary builder-tooltip"
              data-tooltip="Export components as JSON"
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleShareComponents}
              disabled={components.length === 0}
              className="builder-button-secondary builder-tooltip"
              data-tooltip="Share components"
            >
              <Share className="w-4 h-4 mr-1" />
              Share
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleResetEditor}
              className="text-destructive hover:text-destructive hover:bg-destructive/10 builder-tooltip"
              data-tooltip="Reset editor and clear all components"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Live Preview */}
      <div className="flex-1 overflow-hidden builder-slide-up">
        <LivePreview />
      </div>
    </div>
  )

  return (
    <div className="builder-theme-visual">
      <EditorLayout
        leftPanel={leftPanel}
        rightPanel={rightPanel}
        className="bg-background"
      >
        {mainContent}
      </EditorLayout>
    </div>
  )
}
