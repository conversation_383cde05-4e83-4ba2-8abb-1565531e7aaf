'use client'

import { useState, useMemo } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  ChevronRight,
  ChevronDown,
  Eye,
  EyeOff,
  Copy,
  Trash2,
  MoreHorizontal,
  Layers,
  Component,
  FileCode,
  Folder,
  FolderOpen
} from 'lucide-react'
import { useEditorStore } from '../stores/editor-store'
import { GeneratedComponent } from '../types'
import { toast } from 'sonner'

interface ComponentTreeNode {
  id: string
  name: string
  type: 'component' | 'folder'
  children?: ComponentTreeNode[]
  component?: GeneratedComponent
  isExpanded?: boolean
  isVisible?: boolean
}

export function DarkComponentTree() {
  const { 
    components, 
    selectedComponentId, 
    selectComponent, 
    deleteComponent,
    updateComponent
  } = useEditorStore()
  
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']))
  const [hiddenComponents, setHiddenComponents] = useState<Set<string>>(new Set())

  // Build tree structure
  const treeData = useMemo(() => {
    const filteredComponents = components.filter(component =>
      searchQuery === '' || 
      component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      component.category.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Group by category
    const grouped = filteredComponents.reduce((acc, component) => {
      if (!acc[component.category]) {
        acc[component.category] = []
      }
      acc[component.category].push(component)
      return acc
    }, {} as Record<string, GeneratedComponent[]>)

    // Convert to tree structure
    const tree: ComponentTreeNode[] = Object.entries(grouped).map(([category, categoryComponents]) => ({
      id: category,
      name: category.charAt(0).toUpperCase() + category.slice(1),
      type: 'folder' as const,
      isExpanded: expandedNodes.has(category),
      children: categoryComponents.map(component => ({
        id: component.id,
        name: component.name,
        type: 'component' as const,
        component,
        isVisible: !hiddenComponents.has(component.id)
      }))
    }))

    return tree
  }, [components, searchQuery, expandedNodes, hiddenComponents])

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const toggleComponentVisibility = (componentId: string) => {
    const newHidden = new Set(hiddenComponents)
    if (newHidden.has(componentId)) {
      newHidden.delete(componentId)
    } else {
      newHidden.add(componentId)
    }
    setHiddenComponents(newHidden)
  }

  const handleSelectComponent = (component: GeneratedComponent) => {
    selectComponent(component.id)
  }

  const handleDeleteComponent = (componentId: string) => {
    if (confirm('Are you sure you want to delete this component?')) {
      deleteComponent(componentId)
      toast.success('Component deleted')
    }
  }

  const handleDuplicateComponent = (component: GeneratedComponent) => {
    const duplicated = {
      ...component,
      id: `${component.id}-copy-${Date.now()}`,
      name: `${component.name} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    // This would need to be implemented in the store
    toast.success('Component duplicated')
  }

  const renderTreeNode = (node: ComponentTreeNode, depth = 0) => {
    const isSelected = node.type === 'component' && node.id === selectedComponentId
    const paddingLeft = depth * 16 + 8

    if (node.type === 'folder') {
      return (
        <div key={node.id}>
          <div
            className="flex items-center py-1 px-2 hover:bg-gray-800 cursor-pointer group"
            style={{ paddingLeft }}
            onClick={() => toggleNode(node.id)}
          >
            {node.isExpanded ? (
              <ChevronDown className="w-4 h-4 text-gray-400 mr-1" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-400 mr-1" />
            )}
            {node.isExpanded ? (
              <FolderOpen className="w-4 h-4 text-blue-400 mr-2" />
            ) : (
              <Folder className="w-4 h-4 text-blue-400 mr-2" />
            )}
            <span className="text-sm text-gray-300 font-medium">{node.name}</span>
            <Badge variant="secondary" className="ml-auto text-xs bg-gray-700 text-gray-400">
              {node.children?.length || 0}
            </Badge>
          </div>
          
          {node.isExpanded && node.children && (
            <div>
              {node.children.map(child => renderTreeNode(child, depth + 1))}
            </div>
          )}
        </div>
      )
    }

    // Component node
    const component = node.component!
    const isVisible = node.isVisible

    return (
      <div
        key={node.id}
        className={`flex items-center py-1 px-2 hover:bg-gray-800 cursor-pointer group ${
          isSelected ? 'bg-blue-600/20 border-l-2 border-blue-400' : ''
        }`}
        style={{ paddingLeft }}
        onClick={() => handleSelectComponent(component)}
      >
        <Component className="w-4 h-4 text-green-400 mr-2" />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <span className={`text-sm truncate ${isSelected ? 'text-white font-medium' : 'text-gray-300'}`}>
              {component.name}
            </span>
            {!isVisible && (
              <EyeOff className="w-3 h-3 text-gray-500" />
            )}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {component.description}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              toggleComponentVisibility(component.id)
            }}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
          >
            {isVisible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              handleDuplicateComponent(component)
            }}
            className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
          >
            <Copy className="w-3 h-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              handleDeleteComponent(component.id)
            }}
            className="h-6 w-6 p-0 text-gray-400 hover:text-red-400 hover:bg-gray-700"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header */}
      <div className="p-3 border-b border-gray-700 bg-gray-800">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Layers className="w-4 h-4 text-purple-400" />
            <h3 className="font-medium text-gray-200">Components</h3>
          </div>
          <Badge variant="secondary" className="text-xs bg-gray-700 text-gray-300">
            {components.length}
          </Badge>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-500" />
          <Input
            placeholder="Search components..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-7 h-7 bg-gray-700 border-gray-600 text-gray-300 placeholder-gray-500 text-xs"
          />
        </div>
      </div>

      {/* Tree Content */}
      <ScrollArea className="flex-1">
        {components.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <Layers className="w-8 h-8 mx-auto mb-2 text-gray-600" />
            <div className="text-sm">No components yet</div>
            <div className="text-xs mt-1">Generate components using the AI assistant</div>
          </div>
        ) : treeData.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <Search className="w-8 h-8 mx-auto mb-2 text-gray-600" />
            <div className="text-sm">No components match "{searchQuery}"</div>
          </div>
        ) : (
          <div className="py-2">
            {treeData.map(node => renderTreeNode(node))}
          </div>
        )}
      </ScrollArea>

      {/* Footer Stats */}
      {components.length > 0 && (
        <div className="p-3 border-t border-gray-700 bg-gray-800">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>{components.length} total components</span>
            <span>{hiddenComponents.size} hidden</span>
          </div>
        </div>
      )}
    </div>
  )
}
