'use client'

import { useState, useMemo, useCallback } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Settings,
  Palette,
  Type,
  Zap,
  Database,
  Layout,
  RotateCcw,
  Eye,
  EyeOff,
  Search,
  Lightbulb,
  Copy,
  Save,
  History
} from 'lucide-react'
import { useSelectedComponent, useSelectedComponentProperties, useEditorStore } from '../stores/editor-store'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields/field-renderer'
import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { toast } from 'sonner'

const tabConfig = [
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Colors, spacing, borders, shadows'
  },
  {
    id: 'content',
    label: 'Content',
    icon: Type,
    description: 'Text, images, links, media'
  },
  {
    id: 'behavior',
    label: 'Behavior',
    icon: Zap,
    description: 'Animations, interactions, states'
  },
  {
    id: 'data',
    label: 'Data',
    icon: Database,
    description: 'Data sources, API connections'
  },
  {
    id: 'layout',
    label: 'Layout',
    icon: Layout,
    description: 'Positioning, sizing, alignment'
  }
]

export function PropertiesPanel() {
  const selectedComponent = useSelectedComponent()
  const propertyValues = useSelectedComponentProperties()
  const { updatePropertyValue, resetPropertyValues } = useEditorStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [propertyHistory, setPropertyHistory] = useState<Record<string, any[]>>({})
  const [activeTab, setActiveTab] = useState('appearance')
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Smart suggestions based on component analysis
  const smartSuggestions = useMemo(() => {
    if (!selectedComponent) return []

    return generateSmartSuggestions(selectedComponent, propertyValues)
  }, [selectedComponent, propertyValues])

  // Filter fields based on search
  const filteredFields = useCallback((fields: FieldConfig[]) => {
    if (!searchQuery) return fields

    return fields.filter(field =>
      field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      field.id.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [searchQuery])

  if (!selectedComponent) {
    return (
      <div className="h-full flex flex-col builder-panel builder-theme-visual">
        <div className="builder-panel-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Settings className="w-5 h-5 text-muted-foreground" />
              <h2 className="text-lg font-semibold text-foreground">Properties</h2>
            </div>
            <div className="builder-help-indicator" data-tooltip="Configure component properties">
              ?
            </div>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4 builder-slide-up">
            <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center mx-auto">
              <EyeOff className="w-8 h-8 text-muted-foreground" />
            </div>
            <div>
              <div className="text-lg font-medium mb-2 text-foreground">No Component Selected</div>
              <div className="text-sm text-muted-foreground max-w-48">
                Select a component from the preview or generate one with AI to start editing
              </div>
            </div>
            <div className="flex items-center justify-center gap-2 mt-4">
              <div className="w-2 h-2 bg-current opacity-20 rounded-full"></div>
              <div className="w-2 h-2 bg-current opacity-40 rounded-full"></div>
              <div className="w-2 h-2 bg-current opacity-60 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleFieldChange = (fieldId: string, value: any) => {
    // Store in history for undo functionality
    setPropertyHistory(prev => ({
      ...prev,
      [fieldId]: [...(prev[fieldId] || []), propertyValues[fieldId]].slice(-10) // Keep last 10 values
    }))

    updatePropertyValue(selectedComponent.id, fieldId, value)
  }

  const handleReset = () => {
    resetPropertyValues(selectedComponent.id)
    setPropertyHistory({})
    toast.success('Properties reset to default values')
  }

  const handleCopyProperties = () => {
    navigator.clipboard.writeText(JSON.stringify(propertyValues, null, 2))
    toast.success('Properties copied to clipboard')
  }

  const handleSaveAsPreset = () => {
    // This would save the current property values as a preset
    const presetName = prompt('Enter preset name:')
    if (presetName) {
      // Save to local storage or API
      localStorage.setItem(`preset_${presetName}`, JSON.stringify(propertyValues))
      toast.success(`Preset "${presetName}" saved`)
    }
  }

  const applySuggestion = (suggestion: SmartSuggestion) => {
    Object.entries(suggestion.properties).forEach(([fieldId, value]) => {
      handleFieldChange(fieldId, value)
    })
    toast.success(`Applied suggestion: ${suggestion.title}`)
  }

  const renderFieldSection = (sectionId: string, fields: FieldConfig[]) => {
    const visibleFields = filteredFields(fields)

    if (!fields || fields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No {sectionId} properties available</div>
        </div>
      )
    }

    if (searchQuery && visibleFields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No properties match "{searchQuery}"</div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchQuery('')}
            className="mt-2 text-xs"
          >
            Clear search
          </Button>
        </div>
      )
    }

    return (
      <div className="space-y-4 p-4">
        {visibleFields.map((field) => (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <CustomFieldRenderer
                  config={field}
                  value={propertyValues[field.id]}
                  onChange={(value) => handleFieldChange(field.id, value)}
                  onValidate={(isValid, message) => {
                    if (!isValid && message) {
                      toast.error(`${field.label}: ${message}`)
                    }
                  }}
                  className="w-full"
                />
              </div>
              {propertyHistory[field.id] && propertyHistory[field.id].length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const lastValue = propertyHistory[field.id].pop()
                    if (lastValue !== undefined) {
                      updatePropertyValue(selectedComponent.id, field.id, lastValue)
                      setPropertyHistory(prev => ({
                        ...prev,
                        [field.id]: prev[field.id] || []
                      }))
                    }
                  }}
                  className="ml-2 h-6 w-6 p-0"
                  title="Undo last change"
                >
                  <History className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col builder-panel builder-theme-visual">
      {/* Enhanced Header */}
      <div className="builder-panel-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold text-foreground">Properties</h2>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-7 px-2 builder-button-ghost builder-tooltip"
              data-tooltip="Toggle panel collapse"
            >
              <Eye className={`w-3 h-3 transition-transform ${isCollapsed ? 'rotate-180' : ''}`} />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className="text-xs builder-button-secondary"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Reset
            </Button>
          </div>
        </div>

        {/* Enhanced Component Info */}
        {!isCollapsed && (
          <div className="mt-3 builder-slide-up">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-current/10 rounded-lg flex items-center justify-center">
                <Eye className="w-4 h-4 text-current" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-foreground">{selectedComponent.name}</span>
                  <Badge variant="secondary" className="text-xs builder-status-ready">
                    {selectedComponent.category}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {selectedComponent.description}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Search and Actions */}
      {!isCollapsed && (
        <div className="properties-section">
          <div className="properties-section-content">
            <div className="flex items-center space-x-2 mb-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search properties..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-9 text-sm border-border focus:border-current transition-colors"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSuggestions(!showSuggestions)}
                className={`builder-button-ghost ${showSuggestions ? 'bg-current/10 text-current border-current/30' : ''}`}
                title="Toggle AI suggestions"
              >
                <Lightbulb className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyProperties}
                className="builder-button-secondary flex-1"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveAsPreset}
                className="builder-button-secondary flex-1"
              >
                <Save className="w-3 h-3 mr-1" />
                Preset
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Smart Suggestions */}
      {!isCollapsed && showSuggestions && smartSuggestions.length > 0 && (
        <div className="properties-section">
          <div className="properties-section-header">
            <Lightbulb className="w-4 h-4 text-current" />
            <span className="font-medium">Smart Suggestions</span>
            <Badge variant="secondary" className="text-xs ml-auto">
              AI
            </Badge>
          </div>
          <div className="properties-section-content">
            <div className="space-y-3">
              {smartSuggestions.slice(0, 3).map((suggestion, index) => (
                <Card key={index} className="builder-panel border-current/20 hover:border-current/40 transition-colors">
                  <div className="p-3">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="text-xs font-medium text-foreground">{suggestion.title}</div>
                          <Badge
                            variant="outline"
                            className={`text-[10px] px-1.5 py-0.5 ${
                              suggestion.category === 'accessibility' ? 'border-green-200 text-green-700' :
                              suggestion.category === 'design' ? 'border-blue-200 text-blue-700' :
                              suggestion.category === 'interaction' ? 'border-purple-200 text-purple-700' :
                              'border-orange-200 text-orange-700'
                            }`}
                          >
                            {suggestion.category}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                        <div className="flex items-center gap-1 mt-2">
                          <div className="text-[10px] text-muted-foreground">Confidence:</div>
                          <div className="flex gap-0.5">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <div
                                key={i}
                                className={`w-1 h-1 rounded-full ${
                                  i < Math.floor(suggestion.confidence * 5) ? 'bg-current' : 'bg-current/20'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => applySuggestion(suggestion)}
                        className="text-xs h-7 px-2 builder-button-primary"
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Properties Tabs */}
      {!isCollapsed && (
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="properties-section-header">
              <TabsList className="grid w-full grid-cols-5 h-auto p-1 bg-muted/30">
                {tabConfig.map((tab) => {
                  const Icon = tab.icon
                  const allFields = selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                  const visibleFields = filteredFields(allFields)
                  const fieldCount = searchQuery ? visibleFields.length : allFields.length

                  return (
                    <TabsTrigger
                      key={tab.id}
                      value={tab.id}
                      className="flex flex-col items-center p-2 text-xs data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
                    >
                      <Icon className="w-4 h-4 mb-1" />
                      <span className="font-medium">{tab.label}</span>
                      {fieldCount > 0 && (
                        <Badge variant="secondary" className="text-[10px] px-1 py-0 mt-1">
                          {fieldCount}
                        </Badge>
                      )}
                      {searchQuery && visibleFields.length !== allFields.length && (
                        <Badge variant="outline" className="text-[10px] px-1 py-0 mt-1 border-orange-200 text-orange-600">
                          {visibleFields.length}/{allFields.length}
                        </Badge>
                      )}
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </div>

            <div className="flex-1 overflow-hidden">
              {tabConfig.map((tab) => (
                <TabsContent
                  key={tab.id}
                  value={tab.id}
                  className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
                >
                  <div className="properties-section-header">
                    <tab.icon className="w-4 h-4 text-current" />
                    <span className="font-medium">{tab.label}</span>
                    <div className="builder-help-indicator ml-auto" data-tooltip={tab.description}>
                      ?
                    </div>
                  </div>

                  <ScrollArea className="flex-1">
                    <div className="properties-section-content">
                      {renderFieldSection(
                        tab.id,
                        selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      )}

      {/* Enhanced Footer */}
      {!isCollapsed && (
        <div className="properties-section">
          <div className="properties-section-content">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Settings className="w-3 h-3" />
                <span>{Object.values(selectedComponent.propertiesConfig).flat().length} properties</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-muted-foreground">Live</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Smart suggestions interface and generator
interface SmartSuggestion {
  title: string
  description: string
  properties: Record<string, any>
  category: string
  confidence: number
}

function generateSmartSuggestions(
  component: any,
  currentProperties: Record<string, any>
): SmartSuggestion[] {
  const suggestions: SmartSuggestion[] = []

  // Color harmony suggestions
  if (currentProperties.backgroundColor && !currentProperties.textColor) {
    const bgColor = currentProperties.backgroundColor
    const suggestedTextColor = getContrastingColor(bgColor)

    suggestions.push({
      title: 'Improve Text Contrast',
      description: 'Add contrasting text color for better readability',
      properties: { textColor: suggestedTextColor },
      category: 'accessibility',
      confidence: 0.9
    })
  }

  // Spacing consistency
  if (currentProperties.padding && !currentProperties.margin) {
    suggestions.push({
      title: 'Add Consistent Spacing',
      description: 'Add margin to match your padding for visual balance',
      properties: {
        margin: {
          top: Math.floor(currentProperties.padding.top / 2),
          right: Math.floor(currentProperties.padding.right / 2),
          bottom: Math.floor(currentProperties.padding.bottom / 2),
          left: Math.floor(currentProperties.padding.left / 2)
        }
      },
      category: 'design',
      confidence: 0.7
    })
  }

  // Shadow and border radius pairing
  if (currentProperties.borderRadius > 8 && !currentProperties.shadow) {
    suggestions.push({
      title: 'Add Subtle Shadow',
      description: 'Rounded corners look great with a soft shadow',
      properties: { shadow: '0 2px 8px rgba(0,0,0,0.1)' },
      category: 'design',
      confidence: 0.8
    })
  }

  // Animation suggestions for interactive elements
  if (component.category === 'button' && !currentProperties.hoverEffect) {
    suggestions.push({
      title: 'Add Hover Effect',
      description: 'Make buttons more interactive with hover effects',
      properties: { hoverEffect: 'lift' },
      category: 'interaction',
      confidence: 0.85
    })
  }

  // Responsive suggestions
  if (currentProperties.width === 'w-full' && component.category === 'card') {
    suggestions.push({
      title: 'Optimize for Mobile',
      description: 'Consider fixed width for better mobile experience',
      properties: { width: 'w-[350px]' },
      category: 'responsive',
      confidence: 0.6
    })
  }

  return suggestions.sort((a, b) => b.confidence - a.confidence)
}

function getContrastingColor(backgroundColor: string): string {
  // Simple contrast calculation - in production, use a proper color library
  if (backgroundColor.toLowerCase().includes('dark') || backgroundColor === '#000000') {
    return '#ffffff'
  }
  if (backgroundColor.toLowerCase().includes('light') || backgroundColor === '#ffffff') {
    return '#000000'
  }

  // For hex colors, calculate luminance
  if (backgroundColor.startsWith('#')) {
    const hex = backgroundColor.slice(1)
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

    return luminance > 0.5 ? '#000000' : '#ffffff'
  }

  return '#000000' // Default fallback
}
