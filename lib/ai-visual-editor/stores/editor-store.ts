'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { shallow } from 'zustand/shallow'
import { GeneratedComponent, EditorStore } from '../types'

const initialState = {
  // Components
  components: [],
  selectedComponentId: null,
  
  // Properties
  propertyValues: {},
  
  // UI State
  isGenerating: false,
  previewMode: 'desktop' as const,
  showComponentTree: true,
  
  // Chat
  chatMessages: [],
  isAIResponding: false,
}

export const useEditorStore = create<EditorStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Component management
      addComponent: (component: GeneratedComponent) => {
        set((state) => ({
          components: [...state.components, component],
          selectedComponentId: component.id,
          propertyValues: {
            ...state.propertyValues,
            [component.id]: component.defaultValues
          }
        }))
      },

      updateComponent: (id: string, updates: Partial<GeneratedComponent>) => {
        set((state) => ({
          components: state.components.map(comp =>
            comp.id === id 
              ? { ...comp, ...updates, updatedAt: new Date() }
              : comp
          )
        }))
      },

      deleteComponent: (id: string) => {
        set((state) => {
          const newPropertyValues = { ...state.propertyValues }
          delete newPropertyValues[id]
          
          return {
            components: state.components.filter(comp => comp.id !== id),
            selectedComponentId: state.selectedComponentId === id ? null : state.selectedComponentId,
            propertyValues: newPropertyValues
          }
        })
      },

      selectComponent: (id: string | null) => {
        set({ selectedComponentId: id })
      },

      // Property management
      updatePropertyValue: (componentId: string, fieldId: string, value: any) => {
        set((state) => ({
          propertyValues: {
            ...state.propertyValues,
            [componentId]: {
              ...(state.propertyValues[componentId] || {}),
              [fieldId]: value
            }
          }
        }))
      },

      resetPropertyValues: (componentId: string) => {
        const component = get().components.find(c => c.id === componentId)
        if (component) {
          set((state) => ({
            propertyValues: {
              ...state.propertyValues,
              [componentId]: component.defaultValues
            }
          }))
        }
      },

      // UI actions
      setGenerating: (isGenerating: boolean) => {
        set({ isGenerating })
      },

      setPreviewMode: (mode: 'desktop' | 'tablet' | 'mobile') => {
        set({ previewMode: mode })
      },

      toggleComponentTree: () => {
        set((state) => ({ showComponentTree: !state.showComponentTree }))
      },

      // Chat actions
      addChatMessage: (message: any) => {
        set((state) => ({
          chatMessages: [...state.chatMessages, message]
        }))
      },

      setAIResponding: (isResponding: boolean) => {
        set({ isAIResponding: isResponding })
      },

      clearChat: () => {
        set({ chatMessages: [] })
      },
    }),
    {
      name: 'ai-visual-editor-store',
      partialize: (state) => ({
        components: state.components,
        propertyValues: state.propertyValues,
        previewMode: state.previewMode,
        showComponentTree: state.showComponentTree,
      }),
    }
  )
)

// Selectors for better performance
export const useSelectedComponent = () => {
  return useEditorStore((state) => {
    const selectedId = state.selectedComponentId
    return selectedId ? state.components.find(c => c.id === selectedId) : null
  })
}

// Create a stable empty object to avoid infinite re-renders
const EMPTY_PROPERTIES = {}

export const useSelectedComponentProperties = () => {
  return useEditorStore((state) => {
    const selectedId = state.selectedComponentId
    if (!selectedId) return EMPTY_PROPERTIES
    return state.propertyValues[selectedId] || EMPTY_PROPERTIES
  })
}

export const useComponentById = (id: string) => {
  return useEditorStore((state) => 
    state.components.find(c => c.id === id)
  )
}

export const useComponentProperties = (id: string) => {
  return useEditorStore((state) => 
    state.propertyValues[id] || EMPTY_PROPERTIES
  )
}
