// Analytics Service for E-commerce
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '../../types/base'

const prisma = new PrismaClient()

export interface AnalyticsMetrics {
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  conversionRate: number
  topProducts: Array<{
    id: string
    title: string
    sales: number
    revenue: number
  }>
  salesByDay: Array<{
    date: string
    orders: number
    revenue: number
  }>
  customerMetrics: {
    totalCustomers: number
    newCustomers: number
    returningCustomers: number
  }
}

export interface ProductAnalytics {
  productId: string
  views: number
  addToCarts: number
  purchases: number
  conversionRate: number
  revenue: number
}

export class AnalyticsService {
  async getOverviewMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<ApiResponse<AnalyticsMetrics>> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      // Get total orders and revenue
      const orderStats = await prisma.order.aggregate({
        where: {
          status: 'completed',
          ...dateFilter
        },
        _count: true,
        _sum: {
          total: true
        }
      })

      const totalOrders = orderStats._count || 0
      const totalRevenue = Number(orderStats._sum?.total || 0)
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

      // Get top products
      const topProducts = await this.getTopProducts(5, startDate, endDate)

      // Get sales by day
      const salesByDay = await this.getSalesByDay(startDate, endDate)

      // Get customer metrics
      const customerMetrics = await this.getCustomerMetrics(startDate, endDate)

      // Calculate conversion rate (placeholder - would need page view data)
      const conversionRate = 2.5 // Mock value

      const metrics: AnalyticsMetrics = {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        conversionRate,
        topProducts,
        salesByDay,
        customerMetrics
      }

      return {
        success: true,
        data: metrics
      }
    } catch (error) {
      console.error('Analytics service error:', error)
      return {
        success: false,
        error: {
          code: 'ANALYTICS_ERROR',
          message: 'Failed to fetch analytics metrics'
        }
      }
    }
  }

  async getProductAnalytics(
    productId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ApiResponse<ProductAnalytics>> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      // Get product sales data
      const orderItems = await prisma.orderItem.findMany({
        where: {
          productId,
          order: {
            status: 'completed',
            ...dateFilter
          }
        },
        include: {
          order: true
        }
      })

      const purchases = orderItems.length
      const revenue = orderItems.reduce((sum, item) => sum + Number(item.totalPrice), 0)

      // Mock data for views and add to carts (would come from analytics tracking)
      const views = purchases * 10 // Mock: assume 10 views per purchase
      const addToCarts = purchases * 3 // Mock: assume 3 add to carts per purchase

      const conversionRate = views > 0 ? (purchases / views) * 100 : 0

      const analytics: ProductAnalytics = {
        productId,
        views,
        addToCarts,
        purchases,
        conversionRate,
        revenue
      }

      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      console.error('Product analytics error:', error)
      return {
        success: false,
        error: {
          code: 'PRODUCT_ANALYTICS_ERROR',
          message: 'Failed to fetch product analytics'
        }
      }
    }
  }

  private async getTopProducts(
    limit: number,
    startDate?: Date,
    endDate?: Date
  ): Promise<Array<{ id: string; title: string; sales: number; revenue: number }>> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      const topProducts = await prisma.orderItem.groupBy({
        by: ['productId'],
        where: {
          order: {
            status: 'completed',
            ...dateFilter
          }
        },
        _count: {
          productId: true
        },
        _sum: {
          totalPrice: true
        },
        orderBy: {
          _sum: {
            totalPrice: 'desc'
          }
        },
        take: limit
      })

      // Get product details
      const productIds = topProducts.map(p => p.productId)
      const products = await prisma.product.findMany({
        where: {
          id: {
            in: productIds
          }
        },
        select: {
          id: true,
          title: true
        }
      })

      return topProducts.map(item => {
        const product = products.find(p => p.id === item.productId)
        return {
          id: item.productId,
          title: product?.title || 'Unknown Product',
          sales: item._count.productId,
          revenue: Number(item._sum.totalPrice || 0)
        }
      })
    } catch (error) {
      console.error('Top products error:', error)
      return []
    }
  }

  private async getSalesByDay(
    startDate?: Date,
    endDate?: Date
  ): Promise<Array<{ date: string; orders: number; revenue: number }>> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      // This is a simplified version - in production you'd want proper date grouping
      const orders = await prisma.order.findMany({
        where: {
          status: 'completed',
          ...dateFilter
        },
        select: {
          createdAt: true,
          total: true
        },
        orderBy: {
          createdAt: 'asc'
        }
      })

      // Group by date
      const salesByDate = new Map<string, { orders: number; revenue: number }>()

      orders.forEach(order => {
        const date = order.createdAt.toISOString().split('T')[0]
        const existing = salesByDate.get(date) || { orders: 0, revenue: 0 }
        salesByDate.set(date, {
          orders: existing.orders + 1,
          revenue: existing.revenue + Number(order.total)
        })
      })

      return Array.from(salesByDate.entries()).map(([date, data]) => ({
        date,
        ...data
      }))
    } catch (error) {
      console.error('Sales by day error:', error)
      return []
    }
  }

  private async getCustomerMetrics(
    startDate?: Date,
    endDate?: Date
  ): Promise<{ totalCustomers: number; newCustomers: number; returningCustomers: number }> {
    try {
      const dateFilter = this.buildDateFilter(startDate, endDate)

      const totalCustomers = await prisma.customer.count({
        where: dateFilter
      })

      // For simplicity, assume 70% are returning customers
      const newCustomers = Math.floor(totalCustomers * 0.3)
      const returningCustomers = totalCustomers - newCustomers

      return {
        totalCustomers,
        newCustomers,
        returningCustomers
      }
    } catch (error) {
      console.error('Customer metrics error:', error)
      return {
        totalCustomers: 0,
        newCustomers: 0,
        returningCustomers: 0
      }
    }
  }

  private buildDateFilter(startDate?: Date, endDate?: Date) {
    const filter: any = {}

    if (startDate || endDate) {
      filter.createdAt = {}
      if (startDate) {
        filter.createdAt.gte = startDate
      }
      if (endDate) {
        filter.createdAt.lte = endDate
      }
    }

    return filter
  }
}

export const analyticsService = new AnalyticsService()
