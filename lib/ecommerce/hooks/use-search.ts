// React hooks for product search functionality
'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { 
  Product, 
  ProductSearchParams, 
  PaginatedResponse,
  ApiResponse
} from '../types'

export interface SearchSuggestion {
  id: string
  text: string
  type: 'product' | 'category' | 'brand' | 'query'
  count?: number
}

export interface UseSearchOptions {
  query?: string
  filters?: Partial<ProductSearchParams>
  autoSearch?: boolean
  debounceMs?: number
  enableSuggestions?: boolean
}

export interface UseSearchReturn {
  // Search results
  products: Product[]
  suggestions: SearchSuggestion[]
  loading: boolean
  error: { code: string; message: string } | null
  pagination: PaginatedResponse<Product>['pagination'] | null
  
  // Search actions
  search: (query: string, filters?: Partial<ProductSearchParams>) => Promise<void>
  searchWithFilters: (filters: Partial<ProductSearchParams>) => Promise<void>
  getSuggestions: (query: string) => Promise<void>
  clearSearch: () => void
  clearError: () => void
  
  // Search state
  currentQuery: string
  hasSearched: boolean
  isSearching: boolean
}

/**
 * Hook for product search functionality with suggestions and filtering
 */
export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const { 
    query: initialQuery = '', 
    filters: initialFilters = {},
    autoSearch = false,
    debounceMs = 300,
    enableSuggestions = true
  } = options

  const [products, setProducts] = useState<Product[]>([])
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<Product>['pagination'] | null>(null)
  const [currentQuery, setCurrentQuery] = useState(initialQuery)
  const [currentFilters, setCurrentFilters] = useState(initialFilters)
  const [hasSearched, setHasSearched] = useState(false)
  const [isSearching, setIsSearching] = useState(false)

  // Debounced search function
  const debouncedSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout
    
    return (query: string, filters: Partial<ProductSearchParams> = {}) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        performSearch(query, filters)
      }, debounceMs)
    }
  }, [debounceMs])

  const performSearch = useCallback(async (query: string, filters: Partial<ProductSearchParams> = {}) => {
    if (!query.trim() && Object.keys(filters).length === 0) {
      setProducts([])
      setPagination(null)
      setHasSearched(false)
      return
    }

    setLoading(true)
    setIsSearching(true)
    setError(null)
    setHasSearched(true)

    try {
      const searchParams: ProductSearchParams = {
        search: query.trim(),
        status: 'active',
        limit: 24,
        ...filters
      }

      // Remove empty values
      Object.keys(searchParams).forEach(key => {
        if (searchParams[key as keyof ProductSearchParams] === '' || 
            searchParams[key as keyof ProductSearchParams] === undefined) {
          delete searchParams[key as keyof ProductSearchParams]
        }
      })

      const params = new URLSearchParams()
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, String(value))
        }
      })

      const response = await fetch(`/api/e-commerce/products/search?${params}`)
      const result: ApiResponse<PaginatedResponse<Product>> = await response.json()

      if (result.success && result.data) {
        setProducts(result.data.data || [])
        setPagination(result.data.pagination || null)
        setCurrentQuery(query)
        setCurrentFilters(filters)
      } else {
        setError(result.error || { code: 'SEARCH_ERROR', message: 'Failed to search products' })
        setProducts([])
        setPagination(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
      setIsSearching(false)
    }
  }, [])

  const getSuggestions = useCallback(async (query: string) => {
    if (!enableSuggestions || !query.trim() || query.length < 2) {
      setSuggestions([])
      return
    }

    try {
      const response = await fetch(`/api/e-commerce/search/suggestions?q=${encodeURIComponent(query)}`)
      const result: ApiResponse<SearchSuggestion[]> = await response.json()

      if (result.success && result.data) {
        setSuggestions(result.data)
      } else {
        setSuggestions([])
      }
    } catch (err) {
      console.error('Failed to fetch suggestions:', err)
      setSuggestions([])
    }
  }, [enableSuggestions])

  const search = useCallback(async (query: string, filters: Partial<ProductSearchParams> = {}) => {
    await performSearch(query, { ...currentFilters, ...filters })
  }, [performSearch, currentFilters])

  const searchWithFilters = useCallback(async (filters: Partial<ProductSearchParams>) => {
    await performSearch(currentQuery, { ...currentFilters, ...filters })
  }, [performSearch, currentQuery, currentFilters])

  const clearSearch = useCallback(() => {
    setProducts([])
    setSuggestions([])
    setPagination(null)
    setCurrentQuery('')
    setCurrentFilters({})
    setHasSearched(false)
    setError(null)
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Auto-search on initial query
  useEffect(() => {
    if (autoSearch && initialQuery) {
      debouncedSearch(initialQuery, initialFilters)
    }
  }, [autoSearch, initialQuery, initialFilters, debouncedSearch])

  // Get suggestions when query changes
  useEffect(() => {
    if (enableSuggestions && currentQuery) {
      getSuggestions(currentQuery)
    }
  }, [currentQuery, enableSuggestions, getSuggestions])

  return {
    // Search results
    products,
    suggestions,
    loading,
    error,
    pagination,
    
    // Search actions
    search,
    searchWithFilters,
    getSuggestions,
    clearSearch,
    clearError,
    
    // Search state
    currentQuery,
    hasSearched,
    isSearching
  }
}

/**
 * Hook for search suggestions only (lightweight)
 */
export function useSearchSuggestions(query: string, enabled: boolean = true) {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [loading, setLoading] = useState(false)

  const fetchSuggestions = useCallback(async (searchQuery: string) => {
    if (!enabled || !searchQuery.trim() || searchQuery.length < 2) {
      setSuggestions([])
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/e-commerce/search/suggestions?q=${encodeURIComponent(searchQuery)}`)
      const result: ApiResponse<SearchSuggestion[]> = await response.json()

      if (result.success && result.data) {
        setSuggestions(result.data)
      } else {
        setSuggestions([])
      }
    } catch (err) {
      console.error('Failed to fetch suggestions:', err)
      setSuggestions([])
    } finally {
      setLoading(false)
    }
  }, [enabled])

  // Debounced suggestions
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchSuggestions(query)
    }, 200)

    return () => clearTimeout(timeoutId)
  }, [query, fetchSuggestions])

  return {
    suggestions,
    loading
  }
}

/**
 * Hook for popular/trending searches
 */
export function usePopularSearches() {
  const [searches, setSearches] = useState<SearchSuggestion[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchPopularSearches = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/e-commerce/search/popular')
        const result: ApiResponse<SearchSuggestion[]> = await response.json()

        if (result.success && result.data) {
          setSearches(result.data)
        }
      } catch (err) {
        console.error('Failed to fetch popular searches:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPopularSearches()
  }, [])

  return {
    searches,
    loading
  }
}
