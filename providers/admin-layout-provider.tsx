'use client'

import { createContext, useContext } from 'react'

const AdminLayoutContext = createContext<{
  // Simplified - no complex state needed
} | null>(null)

export function AdminLayoutProvider({
  children
}: {
  children: React.ReactNode
}) {
  // Simplified provider - just wraps children
  return (
    <AdminLayoutContext.Provider value={{}}>
      {children}
    </AdminLayoutContext.Provider>
  )
}

export function useAdminLayout() {
  const context = useContext(AdminLayoutContext)
  if (!context) {
    throw new Error('useAdminLayout must be used within an AdminLayoutProvider')
  }
  return context
}
