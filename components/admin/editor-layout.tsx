'use client'

import { cn } from '@/lib/utils'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable'
import { Button } from '@/components/ui/button'
import { PanelLeftClose, PanelRightClose } from 'lucide-react'
import { useState } from 'react'

interface EditorLayoutProps {
  children: React.ReactNode
  leftPanel?: React.ReactNode
  rightPanel?: React.ReactNode
  className?: string
}

export function EditorLayout({
  children,
  leftPanel,
  rightPanel,
  className
}: EditorLayoutProps) {
  // Simple local state for panel visibility and sizes
  const [leftPanelOpen, setLeftPanelOpen] = useState(true)
  const [rightPanelOpen, setRightPanelOpen] = useState(true)
  const [leftSize, setLeftSize] = useState(25)
  const [rightSize, setRightSize] = useState(25)

  return (
    <div className={cn('h-screen flex flex-col overflow-hidden bg-background', className)}>
      {/* Header with panel controls */}
      <div className="flex h-12 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          {leftPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLeftPanelOpen(!leftPanelOpen)}
              className="h-8 w-8 p-0"
            >
              <PanelLeftClose className={cn(
                "h-4 w-4 transition-transform",
                leftPanelOpen && "rotate-180"
              )} />
            </Button>
          )}
          <span className="text-sm font-medium">Editor</span>
        </div>
        <div className="flex items-center gap-2">
          {rightPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setRightPanelOpen(!rightPanelOpen)}
              className="h-8 w-8 p-0"
            >
              <PanelRightClose className={cn(
                "h-4 w-4 transition-transform",
                rightPanelOpen && "rotate-180"
              )} />
            </Button>
          )}
        </div>
      </div>

      {/* Main content with resizable panels */}
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 overflow-hidden"
        onLayout={(sizes) => {
          // Update panel sizes when layout changes
          const [left, , right] = sizes
          if (left) setLeftSize(left)
          if (right) setRightSize(right)
        }}
      >
        {/* Left Panel */}
        {leftPanel && leftPanelOpen && (
          <>
            <ResizablePanel
              defaultSize={leftSize}
              minSize={15}
              maxSize={40}
              className="overflow-auto"
            >
              {leftPanel}
            </ResizablePanel>
            <ResizableHandle withHandle />
          </>
        )}

        {/* Main Content */}
        <ResizablePanel
          defaultSize={leftPanelOpen && rightPanelOpen ? 100 - leftSize - rightSize : 100 - (leftPanelOpen ? leftSize : 0) - (rightPanelOpen ? rightSize : 0)}
          className="overflow-auto"
        >
          {children}
        </ResizablePanel>

        {/* Right Panel */}
        {rightPanel && rightPanelOpen && (
          <>
            <ResizableHandle withHandle />
            <ResizablePanel
              defaultSize={rightSize}
              minSize={15}
              maxSize={40}
              className="overflow-auto"
            >
              {rightPanel}
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>
    </div>
  )
}
