'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { 
  Save, 
  Loader2, 
  Package, 
  Truck, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Mail,
  Download
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types'

// Bulk action validation schema
const bulkActionSchema = z.object({
  action: z.enum(['update_status', 'update_fulfillment', 'update_financial', 'send_notification', 'export']),
  status: z.enum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded', 'returned']).optional(),
  financialStatus: z.enum(['pending', 'authorized', 'partially_paid', 'paid', 'partially_refunded', 'refunded', 'voided']).optional(),
  fulfillmentStatus: z.enum(['unfulfilled', 'partially_fulfilled', 'fulfilled', 'shipped', 'delivered', 'returned', 'cancelled']).optional(),
  trackingNumber: z.string().max(100, 'Tracking number is too long').optional(),
  trackingUrl: z.string().url('Invalid tracking URL').optional().or(z.literal('')),
  notificationMessage: z.string().max(1000, 'Message is too long').optional(),
  internalNote: z.string().max(500, 'Internal note is too long').optional(),
  notifyCustomers: z.boolean().default(true),
})

type BulkActionFormData = z.infer<typeof bulkActionSchema>

interface OrderBulkActionsProps {
  selectedOrders: Order[]
  onBulkAction?: (action: string, orders: Order[], data: any) => Promise<void>
  onClose?: () => void
}

export function OrderBulkActions({ selectedOrders, onBulkAction, onClose }: OrderBulkActionsProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<BulkActionFormData>({
    resolver: zodResolver(bulkActionSchema),
    mode: 'onChange',
    defaultValues: {
      action: 'update_status',
      notifyCustomers: true,
    },
  })

  const watchedAction = watch('action')

  // Function to show validation errors as toasts
  const showValidationErrors = () => {
    if (Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, error]) => {
        if (error?.message) {
          const fieldName = field
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
          toast.error(`${fieldName}: ${error.message}`)
        }
      })
      return true
    }
    return false
  }

  const handleBulkActionSubmit = async (data: BulkActionFormData) => {
    console.log('Bulk action started:', data)
    
    try {
      setLoading(true)
      setError(null)

      // Check for validation errors
      if (showValidationErrors()) {
        toast.error('Please fix the validation errors before submitting')
        return
      }

      if (selectedOrders.length === 0) {
        toast.error('No orders selected')
        return
      }

      // Prepare action data based on action type
      let actionData: any = {
        notifyCustomers: data.notifyCustomers,
        internalNote: data.internalNote,
      }

      switch (data.action) {
        case 'update_status':
          if (!data.status) {
            toast.error('Please select a status')
            return
          }
          actionData.status = data.status
          break

        case 'update_fulfillment':
          if (!data.fulfillmentStatus) {
            toast.error('Please select a fulfillment status')
            return
          }
          actionData.fulfillmentStatus = data.fulfillmentStatus
          if (data.trackingNumber) actionData.trackingNumber = data.trackingNumber
          if (data.trackingUrl) actionData.trackingUrl = data.trackingUrl
          break

        case 'update_financial':
          if (!data.financialStatus) {
            toast.error('Please select a financial status')
            return
          }
          actionData.financialStatus = data.financialStatus
          break

        case 'send_notification':
          if (!data.notificationMessage) {
            toast.error('Please enter a notification message')
            return
          }
          actionData.message = data.notificationMessage
          break

        case 'export':
          // No additional data needed for export
          break
      }

      console.log('Executing bulk action:', data.action, 'for', selectedOrders.length, 'orders')

      if (onBulkAction) {
        await onBulkAction(data.action, selectedOrders, actionData)
      }

      toast.success(`Bulk action completed for ${selectedOrders.length} orders`)
      
      if (onClose) {
        onClose()
      }
    } catch (error) {
      console.error('Error executing bulk action:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setError(errorMessage)
      toast.error(`Failed to execute bulk action: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'update_status': return CheckCircle
      case 'update_fulfillment': return Package
      case 'update_financial': return CheckCircle
      case 'send_notification': return Mail
      case 'export': return Download
      default: return Package
    }
  }

  const ActionIcon = getActionIcon(watchedAction)

  return (
    <form onSubmit={handleSubmit(handleBulkActionSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Bulk Actions
          </h2>
          <p className="text-muted-foreground">
            Apply actions to {selectedOrders.length} selected orders
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {onClose && (
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting || loading}>
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={loading || isSubmitting}
            className="min-w-[120px]"
          >
            {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <ActionIcon className="mr-2 h-4 w-4" />
            Execute Action
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Selected Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Selected Orders ({selectedOrders.length})</CardTitle>
          <CardDescription>
            The following orders will be affected by this action
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {selectedOrders.slice(0, 10).map((order) => (
              <Badge key={order.id} variant="secondary">
                #{order.orderNumber}
              </Badge>
            ))}
            {selectedOrders.length > 10 && (
              <Badge variant="outline">
                +{selectedOrders.length - 10} more
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Action</CardTitle>
          <CardDescription>
            Choose the action to apply to all selected orders
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="action">Action Type</Label>
            <Select
              value={watchedAction}
              onValueChange={(value: any) => setValue('action', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="update_status">Update Order Status</SelectItem>
                <SelectItem value="update_fulfillment">Update Fulfillment Status</SelectItem>
                <SelectItem value="update_financial">Update Financial Status</SelectItem>
                <SelectItem value="send_notification">Send Customer Notification</SelectItem>
                <SelectItem value="export">Export Orders</SelectItem>
              </SelectContent>
            </Select>
            {errors.action && (
              <p className="text-sm text-red-500">{errors.action.message}</p>
            )}
          </div>

          {/* Action-specific fields */}
          {watchedAction === 'update_status' && (
            <div className="space-y-2">
              <Label htmlFor="status">New Order Status</Label>
              <Select
                value={watch('status')}
                onValueChange={(value: any) => setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="returned">Returned</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {watchedAction === 'update_fulfillment' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="fulfillmentStatus">New Fulfillment Status</Label>
                <Select
                  value={watch('fulfillmentStatus')}
                  onValueChange={(value: any) => setValue('fulfillmentStatus', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fulfillment status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unfulfilled">Unfulfilled</SelectItem>
                    <SelectItem value="partially_fulfilled">Partially Fulfilled</SelectItem>
                    <SelectItem value="fulfilled">Fulfilled</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="returned">Returned</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingNumber">Tracking Number (Optional)</Label>
                  <Input
                    id="trackingNumber"
                    {...register('trackingNumber')}
                    placeholder="Enter tracking number"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trackingUrl">Tracking URL (Optional)</Label>
                  <Input
                    id="trackingUrl"
                    type="url"
                    {...register('trackingUrl')}
                    placeholder="https://tracking.example.com/..."
                  />
                </div>
              </div>
            </>
          )}

          {watchedAction === 'update_financial' && (
            <div className="space-y-2">
              <Label htmlFor="financialStatus">New Financial Status</Label>
              <Select
                value={watch('financialStatus')}
                onValueChange={(value: any) => setValue('financialStatus', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select financial status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="authorized">Authorized</SelectItem>
                  <SelectItem value="partially_paid">Partially Paid</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="partially_refunded">Partially Refunded</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="voided">Voided</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {watchedAction === 'send_notification' && (
            <div className="space-y-2">
              <Label htmlFor="notificationMessage">Notification Message</Label>
              <Textarea
                id="notificationMessage"
                {...register('notificationMessage')}
                placeholder="Enter the message to send to customers..."
                rows={4}
              />
              {errors.notificationMessage && (
                <p className="text-sm text-red-500">{errors.notificationMessage.message}</p>
              )}
            </div>
          )}

          <Separator />

          {/* Common fields */}
          <div className="space-y-2">
            <Label htmlFor="internalNote">Internal Note (Optional)</Label>
            <Textarea
              id="internalNote"
              {...register('internalNote')}
              placeholder="Add an internal note about this bulk action..."
              rows={2}
            />
          </div>

          {watchedAction !== 'export' && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="notifyCustomers"
                checked={watch('notifyCustomers')}
                onCheckedChange={(checked) => setValue('notifyCustomers', !!checked)}
              />
              <Label htmlFor="notifyCustomers">Notify customers via email</Label>
            </div>
          )}
        </CardContent>
      </Card>
    </form>
  )
}
