'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Zap, 
  Settings, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  Mail,
  Package,
  Truck,
  DollarSign,
  Users,
  BarChart3,
  Activity,
  Plus,
  Edit,
  Trash2,
  Save,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types'

// Automation rule schema
const automationRuleSchema = z.object({
  name: z.string().min(1, 'Rule name is required').max(100, 'Name is too long'),
  description: z.string().max(500, 'Description is too long').optional(),
  enabled: z.boolean().default(true),
  trigger: z.enum(['order_created', 'payment_received', 'status_change', 'time_based', 'inventory_low']),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.enum(['equals', 'not_equals', 'greater_than', 'less_than', 'contains', 'not_contains']),
    value: z.string(),
  })).default([]),
  actions: z.array(z.object({
    type: z.enum(['update_status', 'send_email', 'create_fulfillment', 'update_inventory', 'assign_tag', 'webhook']),
    parameters: z.record(z.any()),
  })).min(1, 'At least one action is required'),
  priority: z.number().min(1).max(10).default(5),
  cooldown: z.number().min(0).default(0), // Minutes
})

type AutomationRuleFormData = z.infer<typeof automationRuleSchema>

interface AutomationRule extends AutomationRuleFormData {
  id: string
  createdAt: Date
  lastRun?: Date
  runCount: number
  successCount: number
  errorCount: number
}

interface OrderAutomationEngineProps {
  orders: Order[]
  onRuleUpdate?: (rule: AutomationRule) => void
  onRuleDelete?: (ruleId: string) => void
}

// Mock automation rules
const mockRules: AutomationRule[] = [
  {
    id: '1',
    name: 'Auto-confirm paid orders',
    description: 'Automatically confirm orders when payment is received',
    enabled: true,
    trigger: 'payment_received',
    conditions: [
      { field: 'financial_status', operator: 'equals', value: 'paid' }
    ],
    actions: [
      { type: 'update_status', parameters: { status: 'confirmed' } },
      { type: 'send_email', parameters: { template: 'order_confirmed' } }
    ],
    priority: 8,
    cooldown: 0,
    createdAt: new Date(),
    runCount: 156,
    successCount: 154,
    errorCount: 2,
  },
  {
    id: '2',
    name: 'High-value order alerts',
    description: 'Send notifications for orders over R1000',
    enabled: true,
    trigger: 'order_created',
    conditions: [
      { field: 'total_amount', operator: 'greater_than', value: '1000' }
    ],
    actions: [
      { type: 'send_email', parameters: { template: 'high_value_alert', recipient: '<EMAIL>' } },
      { type: 'assign_tag', parameters: { tag: 'high-value' } }
    ],
    priority: 7,
    cooldown: 0,
    createdAt: new Date(),
    runCount: 23,
    successCount: 23,
    errorCount: 0,
  },
  {
    id: '3',
    name: 'Auto-fulfill digital products',
    description: 'Automatically fulfill orders containing only digital products',
    enabled: true,
    trigger: 'status_change',
    conditions: [
      { field: 'status', operator: 'equals', value: 'confirmed' },
      { field: 'product_type', operator: 'equals', value: 'digital' }
    ],
    actions: [
      { type: 'create_fulfillment', parameters: { type: 'digital' } },
      { type: 'update_status', parameters: { status: 'fulfilled' } }
    ],
    priority: 9,
    cooldown: 0,
    createdAt: new Date(),
    runCount: 45,
    successCount: 44,
    errorCount: 1,
  },
]

// Available triggers
const TRIGGERS = [
  { value: 'order_created', label: 'Order Created', description: 'When a new order is placed' },
  { value: 'payment_received', label: 'Payment Received', description: 'When payment is confirmed' },
  { value: 'status_change', label: 'Status Change', description: 'When order status changes' },
  { value: 'time_based', label: 'Time Based', description: 'At scheduled intervals' },
  { value: 'inventory_low', label: 'Low Inventory', description: 'When inventory falls below threshold' },
]

// Available actions
const ACTIONS = [
  { value: 'update_status', label: 'Update Status', description: 'Change order status' },
  { value: 'send_email', label: 'Send Email', description: 'Send notification email' },
  { value: 'create_fulfillment', label: 'Create Fulfillment', description: 'Automatically fulfill order' },
  { value: 'update_inventory', label: 'Update Inventory', description: 'Adjust inventory levels' },
  { value: 'assign_tag', label: 'Assign Tag', description: 'Add tags to order' },
  { value: 'webhook', label: 'Webhook', description: 'Call external API' },
]

export function OrderAutomationEngine({ orders, onRuleUpdate, onRuleDelete }: OrderAutomationEngineProps) {
  const [rules, setRules] = useState<AutomationRule[]>(mockRules)
  const [selectedRule, setSelectedRule] = useState<AutomationRule | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<AutomationRuleFormData>({
    resolver: zodResolver(automationRuleSchema),
    mode: 'onChange',
    defaultValues: {
      enabled: true,
      priority: 5,
      cooldown: 0,
      conditions: [],
      actions: [],
    },
  })

  // Calculate automation metrics
  const automationMetrics = {
    totalRules: rules.length,
    activeRules: rules.filter(rule => rule.enabled).length,
    totalRuns: rules.reduce((sum, rule) => sum + rule.runCount, 0),
    successRate: rules.reduce((sum, rule) => sum + rule.runCount, 0) > 0 
      ? (rules.reduce((sum, rule) => sum + rule.successCount, 0) / rules.reduce((sum, rule) => sum + rule.runCount, 0)) * 100 
      : 0,
    ordersProcessed: Math.floor(orders.length * 0.7), // Mock: 70% of orders processed by automation
  }

  // Handle rule creation/update
  const handleRuleSubmit = async (data: AutomationRuleFormData) => {
    try {
      setLoading(true)
      
      console.log('Saving automation rule:', data)

      const ruleData: AutomationRule = {
        ...data,
        id: selectedRule?.id || `rule-${Date.now()}`,
        createdAt: selectedRule?.createdAt || new Date(),
        runCount: selectedRule?.runCount || 0,
        successCount: selectedRule?.successCount || 0,
        errorCount: selectedRule?.errorCount || 0,
      }

      if (selectedRule) {
        // Update existing rule
        setRules(prev => prev.map(rule => rule.id === selectedRule.id ? ruleData : rule))
        toast.success(`Rule "${data.name}" updated successfully`)
      } else {
        // Create new rule
        setRules(prev => [...prev, ruleData])
        toast.success(`Rule "${data.name}" created successfully`)
      }

      if (onRuleUpdate) {
        onRuleUpdate(ruleData)
      }

      setSelectedRule(null)
      setIsCreating(false)
      reset()
    } catch (error) {
      console.error('Error saving rule:', error)
      toast.error('Failed to save automation rule')
    } finally {
      setLoading(false)
    }
  }

  // Handle rule deletion
  const handleDeleteRule = async (ruleId: string) => {
    try {
      setRules(prev => prev.filter(rule => rule.id !== ruleId))
      toast.success('Automation rule deleted')
      
      if (onRuleDelete) {
        onRuleDelete(ruleId)
      }
    } catch (error) {
      console.error('Error deleting rule:', error)
      toast.error('Failed to delete automation rule')
    }
  }

  // Toggle rule enabled state
  const toggleRuleEnabled = async (ruleId: string) => {
    try {
      setRules(prev => prev.map(rule => 
        rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
      ))
      
      const rule = rules.find(r => r.id === ruleId)
      toast.success(`Rule "${rule?.name}" ${rule?.enabled ? 'disabled' : 'enabled'}`)
    } catch (error) {
      console.error('Error toggling rule:', error)
      toast.error('Failed to toggle automation rule')
    }
  }

  // Start editing a rule
  const startEditRule = (rule: AutomationRule) => {
    setSelectedRule(rule)
    setIsCreating(true)
    setActiveTab('create')
    
    // Populate form with rule data
    Object.entries(rule).forEach(([key, value]) => {
      if (key in automationRuleSchema.shape) {
        setValue(key as keyof AutomationRuleFormData, value)
      }
    })
  }

  // Start creating a new rule
  const startCreateRule = () => {
    setSelectedRule(null)
    setIsCreating(true)
    setActiveTab('create')
    reset()
  }

  // Cancel editing
  const cancelEdit = () => {
    setSelectedRule(null)
    setIsCreating(false)
    setActiveTab('overview')
    reset()
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Order Automation Engine</h2>
          <p className="text-muted-foreground">
            Automate order processing workflows with intelligent rules
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button onClick={startCreateRule}>
            <Plus className="mr-2 h-4 w-4" />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Automation Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rules</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{automationMetrics.totalRules}</div>
            <p className="text-xs text-muted-foreground">
              {automationMetrics.activeRules} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Runs</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{automationMetrics.totalRuns}</div>
            <p className="text-xs text-muted-foreground">
              All time executions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {automationMetrics.successRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Successful executions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Processed</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{automationMetrics.ordersProcessed}</div>
            <p className="text-xs text-muted-foreground">
              By automation rules
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24.5h</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Automation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Rules Overview</TabsTrigger>
          <TabsTrigger value="create">
            {isCreating ? (selectedRule ? 'Edit Rule' : 'Create Rule') : 'Create Rule'}
          </TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automation Rules ({rules.length})</CardTitle>
              <CardDescription>
                Manage your automated order processing workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              {rules.length === 0 ? (
                <div className="text-center py-8">
                  <Zap className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold text-muted-foreground">No automation rules</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Create your first automation rule to streamline order processing
                  </p>
                  <div className="mt-6">
                    <Button onClick={startCreateRule}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create Rule
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {rules.map((rule) => (
                    <div key={rule.id} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium">{rule.name}</h4>
                            <Badge variant={rule.enabled ? "default" : "secondary"}>
                              {rule.enabled ? 'Active' : 'Disabled'}
                            </Badge>
                            <Badge variant="outline">
                              Priority {rule.priority}
                            </Badge>
                          </div>
                          {rule.description && (
                            <p className="text-sm text-muted-foreground mb-2">{rule.description}</p>
                          )}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Trigger:</span>
                              <p className="font-medium">
                                {TRIGGERS.find(t => t.value === rule.trigger)?.label}
                              </p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Actions:</span>
                              <p className="font-medium">{rule.actions.length} configured</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Runs:</span>
                              <p className="font-medium">{rule.runCount}</p>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Success Rate:</span>
                              <p className="font-medium">
                                {rule.runCount > 0 ? ((rule.successCount / rule.runCount) * 100).toFixed(1) : 0}%
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={rule.enabled}
                            onCheckedChange={() => toggleRuleEnabled(rule.id)}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => startEditRule(rule)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteRule(rule.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {rule.runCount > 0 && (
                        <div className="pt-3 border-t">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Performance</span>
                            <span className="text-sm text-muted-foreground">
                              {rule.successCount}/{rule.runCount} successful
                            </span>
                          </div>
                          <Progress 
                            value={rule.runCount > 0 ? (rule.successCount / rule.runCount) * 100 : 0} 
                            className="w-full" 
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedRule ? `Edit Rule: ${selectedRule.name}` : 'Create New Automation Rule'}
              </CardTitle>
              <CardDescription>
                Configure triggers, conditions, and actions for automated order processing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(handleRuleSubmit)} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h4 className="font-medium">Basic Information</h4>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Rule Name *</Label>
                      <Input
                        id="name"
                        {...register('name')}
                        placeholder="Enter rule name"
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority (1-10) *</Label>
                      <Input
                        id="priority"
                        type="number"
                        min="1"
                        max="10"
                        {...register('priority', { valueAsNumber: true })}
                        placeholder="5"
                      />
                      {errors.priority && (
                        <p className="text-sm text-red-500">{errors.priority.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      {...register('description')}
                      placeholder="Describe what this rule does..."
                      rows={2}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description.message}</p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enabled"
                      checked={watch('enabled')}
                      onCheckedChange={(checked) => setValue('enabled', checked)}
                    />
                    <Label htmlFor="enabled">Enable this rule</Label>
                  </div>
                </div>

                {/* Trigger Configuration */}
                <div className="space-y-4">
                  <h4 className="font-medium">Trigger</h4>

                  <div className="space-y-2">
                    <Label htmlFor="trigger">When should this rule run? *</Label>
                    <Select
                      value={watch('trigger')}
                      onValueChange={(value: any) => setValue('trigger', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select trigger" />
                      </SelectTrigger>
                      <SelectContent>
                        {TRIGGERS.map((trigger) => (
                          <SelectItem key={trigger.value} value={trigger.value}>
                            <div>
                              <p className="font-medium">{trigger.label}</p>
                              <p className="text-sm text-muted-foreground">{trigger.description}</p>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.trigger && (
                      <p className="text-sm text-red-500">{errors.trigger.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cooldown">Cooldown Period (minutes)</Label>
                    <Input
                      id="cooldown"
                      type="number"
                      min="0"
                      {...register('cooldown', { valueAsNumber: true })}
                      placeholder="0"
                    />
                    <p className="text-sm text-muted-foreground">
                      Minimum time between rule executions for the same order
                    </p>
                  </div>
                </div>

                {/* Actions Configuration */}
                <div className="space-y-4">
                  <h4 className="font-medium">Actions</h4>
                  <p className="text-sm text-muted-foreground">
                    What should happen when this rule is triggered?
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {ACTIONS.map((action) => (
                      <div key={action.value} className="p-3 border rounded-lg">
                        <div className="flex items-center space-x-2 mb-2">
                          <input
                            type="checkbox"
                            id={action.value}
                            className="rounded"
                          />
                          <Label htmlFor={action.value} className="font-medium">
                            {action.label}
                          </Label>
                        </div>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex items-center space-x-2 pt-4 border-t">
                  <Button
                    type="submit"
                    disabled={loading || isSubmitting}
                    className="min-w-[120px]"
                  >
                    {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    {selectedRule ? 'Update Rule' : 'Create Rule'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={cancelEdit}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Rule Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Rule Performance</CardTitle>
                <CardDescription>Success rates and execution counts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {rules.map((rule) => (
                    <div key={rule.id}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{rule.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {rule.runCount} runs
                        </span>
                      </div>
                      <Progress
                        value={rule.runCount > 0 ? (rule.successCount / rule.runCount) * 100 : 0}
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {rule.runCount > 0 ? ((rule.successCount / rule.runCount) * 100).toFixed(1) : 0}% success rate
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Automation Impact */}
            <Card>
              <CardHeader>
                <CardTitle>Automation Impact</CardTitle>
                <CardDescription>Time and cost savings from automation</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-green-600">24.5h</div>
                    <p className="text-sm text-muted-foreground">Time Saved</p>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">R12,450</div>
                    <p className="text-sm text-muted-foreground">Cost Savings</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Orders Auto-Processed:</span>
                    <span className="font-medium">{automationMetrics.ordersProcessed}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Manual Interventions Avoided:</span>
                    <span className="font-medium">89</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Average Processing Time:</span>
                    <span className="font-medium">2.3 min</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Error Rate:</span>
                    <span className="font-medium text-green-600">0.8%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest automation rule executions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { rule: 'Auto-confirm paid orders', order: '#ORD-001', status: 'success', time: '2 min ago' },
                    { rule: 'High-value order alerts', order: '#ORD-002', status: 'success', time: '5 min ago' },
                    { rule: 'Auto-fulfill digital products', order: '#ORD-003', status: 'success', time: '8 min ago' },
                    { rule: 'Auto-confirm paid orders', order: '#ORD-004', status: 'error', time: '12 min ago' },
                    { rule: 'High-value order alerts', order: '#ORD-005', status: 'success', time: '15 min ago' },
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                      }`}></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.rule}</p>
                        <p className="text-xs text-muted-foreground">
                          {activity.order} • {activity.time}
                        </p>
                      </div>
                      <Badge variant={activity.status === 'success' ? 'default' : 'destructive'}>
                        {activity.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Trigger Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Trigger Distribution</CardTitle>
                <CardDescription>How often each trigger type is used</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {TRIGGERS.map((trigger) => {
                    const count = rules.filter(rule => rule.trigger === trigger.value).length
                    const percentage = rules.length > 0 ? (count / rules.length) * 100 : 0

                    return (
                      <div key={trigger.value}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">{trigger.label}</span>
                          <span className="text-sm text-muted-foreground">{count} rules</span>
                        </div>
                        <Progress value={percentage} className="w-full" />
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
