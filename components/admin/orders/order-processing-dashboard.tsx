'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Package, 
  Truck, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  Users,
  ShoppingCart,
  DollarSign,
  Calendar,
  BarChart3,
  Activity,
  RefreshCw,
  Filter,
  Download,
  Play,
  Pause,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types'

interface OrderProcessingDashboardProps {
  orders: Order[]
  onRefresh?: () => void
  onProcessOrder?: (order: Order) => void
  onBulkProcess?: (orders: Order[]) => void
}

// Processing queue priorities
const PRIORITY_LEVELS = {
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800', weight: 4 },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800', weight: 3 },
  normal: { label: 'Normal', color: 'bg-blue-100 text-blue-800', weight: 2 },
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800', weight: 1 },
}

// Processing stages
const PROCESSING_STAGES = [
  { id: 'pending', label: 'Pending Review', icon: Clock },
  { id: 'confirmed', label: 'Confirmed', icon: CheckCircle },
  { id: 'picking', label: 'Picking Items', icon: Package },
  { id: 'packing', label: 'Packing', icon: Package },
  { id: 'shipping', label: 'Ready to Ship', icon: Truck },
  { id: 'shipped', label: 'Shipped', icon: CheckCircle },
]

export function OrderProcessingDashboard({ 
  orders, 
  onRefresh, 
  onProcessOrder, 
  onBulkProcess 
}: OrderProcessingDashboardProps) {
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(30) // seconds
  const [activeTab, setActiveTab] = useState('overview')

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !onRefresh) return

    const interval = setInterval(() => {
      onRefresh()
    }, refreshInterval * 1000)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, onRefresh])

  // Calculate processing metrics
  const metrics = useMemo(() => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    // Filter orders for today
    const todayOrders = orders.filter(order => 
      new Date(order.createdAt) >= today
    )

    // Calculate various metrics
    const totalOrders = orders.length
    const todayOrdersCount = todayOrders.length
    const pendingOrders = orders.filter(o => o.status === 'pending' || o.status === 'confirmed').length
    const processingOrders = orders.filter(o => o.status === 'processing').length
    const shippedToday = todayOrders.filter(o => o.status === 'shipped').length
    const deliveredToday = todayOrders.filter(o => o.status === 'delivered').length

    // Calculate revenue
    const todayRevenue = todayOrders.reduce((sum, order) => sum + (order.total?.amount || 0), 0)
    const totalRevenue = orders.reduce((sum, order) => sum + (order.total?.amount || 0), 0)

    // Calculate average processing time (mock data)
    const avgProcessingTime = 2.5 // hours

    // Calculate fulfillment rate
    const fulfillmentRate = totalOrders > 0 ? 
      (orders.filter(o => ['shipped', 'delivered'].includes(o.status)).length / totalOrders) * 100 : 0

    return {
      totalOrders,
      todayOrdersCount,
      pendingOrders,
      processingOrders,
      shippedToday,
      deliveredToday,
      todayRevenue,
      totalRevenue,
      avgProcessingTime,
      fulfillmentRate,
    }
  }, [orders])

  // Get priority for an order
  const getOrderPriority = (order: Order): keyof typeof PRIORITY_LEVELS => {
    const orderDate = new Date(order.createdAt)
    const now = new Date()
    const hoursOld = (now.getTime() - orderDate.getTime()) / (1000 * 60 * 60)
    
    // High value orders
    if ((order.total?.amount || 0) > 1000) return 'high'
    
    // Urgent if over 24 hours old
    if (hoursOld > 24) return 'urgent'
    
    // High priority if over 12 hours old
    if (hoursOld > 12) return 'high'
    
    // Express shipping
    if (order.shippingMethod?.title?.toLowerCase().includes('express')) return 'high'
    
    return 'normal'
  }

  // Get processing queue (orders that need attention)
  const processingQueue = useMemo(() => {
    return orders
      .filter(order => ['pending', 'confirmed', 'processing'].includes(order.status))
      .map(order => ({
        ...order,
        priority: getOrderPriority(order),
        priorityWeight: PRIORITY_LEVELS[getOrderPriority(order)].weight,
      }))
      .sort((a, b) => {
        // Sort by priority weight (descending), then by creation date (ascending)
        if (a.priorityWeight !== b.priorityWeight) {
          return b.priorityWeight - a.priorityWeight
        }
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      })
  }, [orders])

  // Calculate stage distribution
  const stageDistribution = useMemo(() => {
    const distribution = PROCESSING_STAGES.map(stage => ({
      ...stage,
      count: 0,
      orders: [] as Order[],
    }))

    orders.forEach(order => {
      const stageIndex = PROCESSING_STAGES.findIndex(stage => stage.id === order.status)
      if (stageIndex >= 0) {
        distribution[stageIndex].count++
        distribution[stageIndex].orders.push(order)
      }
    })

    return distribution
  }, [orders])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-ZA', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    )
  }

  const handleSelectAll = () => {
    if (selectedOrders.length === processingQueue.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(processingQueue.map(order => order.id))
    }
  }

  const handleBulkProcess = () => {
    const ordersToProcess = orders.filter(order => selectedOrders.includes(order.id))
    if (onBulkProcess && ordersToProcess.length > 0) {
      onBulkProcess(ordersToProcess)
      setSelectedOrders([])
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Order Processing Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and manage order processing workflow
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            {autoRefresh ? <Pause className="mr-2 h-4 w-4" /> : <Play className="mr-2 h-4 w-4" />}
            Auto Refresh {autoRefresh ? 'On' : 'Off'}
          </Button>
          {onRefresh && (
            <Button variant="outline" onClick={onRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          )}
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.todayOrdersCount}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalOrders} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{metrics.pendingOrders}</div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{metrics.processingOrders}</div>
            <p className="text-xs text-muted-foreground">
              In progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped Today</CardTitle>
            <Truck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{metrics.shippedToday}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.deliveredToday} delivered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.todayRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(metrics.totalRevenue)} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fulfillment Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.fulfillmentRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {metrics.avgProcessingTime}h avg time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Processing Pipeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            Processing Pipeline
          </CardTitle>
          <CardDescription>
            Order distribution across processing stages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {stageDistribution.map((stage) => {
              const StageIcon = stage.icon
              return (
                <div key={stage.id} className="text-center">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="p-3 rounded-full bg-muted">
                      <StageIcon className="h-6 w-6" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold">{stage.count}</p>
                      <p className="text-sm text-muted-foreground">{stage.label}</p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="queue">Processing Queue</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest order status changes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.slice(0, 5).map((order) => (
                    <div key={order.id} className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          Order #{order.orderNumber}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {order.customer?.firstName} {order.customer?.lastName} • {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <Badge variant="outline">{order.status}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Fulfillment Rate</span>
                    <span className="text-sm text-muted-foreground">{metrics.fulfillmentRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.fulfillmentRate} className="w-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Processing Efficiency</span>
                    <span className="text-sm text-muted-foreground">85%</span>
                  </div>
                  <Progress value={85} className="w-full" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">On-Time Delivery</span>
                    <span className="text-sm text-muted-foreground">92%</span>
                  </div>
                  <Progress value={92} className="w-full" />
                </div>

                <div className="pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Avg Processing Time:</span>
                    <span className="font-medium">{metrics.avgProcessingTime}h</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Orders per Hour:</span>
                    <span className="font-medium">12.5</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Error Rate:</span>
                    <span className="font-medium text-green-600">0.8%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="queue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Processing Queue ({processingQueue.length})</span>
                <div className="flex items-center space-x-2">
                  {selectedOrders.length > 0 && (
                    <Button onClick={handleBulkProcess} size="sm">
                      Process {selectedOrders.length} Orders
                    </Button>
                  )}
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {selectedOrders.length === processingQueue.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Orders prioritized by urgency and processing requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              {processingQueue.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                  <h3 className="mt-2 text-sm font-semibold text-green-800">All caught up!</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    No orders in the processing queue
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {processingQueue.map((order) => (
                    <div
                      key={order.id}
                      className={`p-4 border rounded-lg transition-colors ${
                        selectedOrders.includes(order.id) ? 'bg-blue-50 border-blue-200' : 'hover:bg-muted/50'
                      }`}
                    >
                      <div className="flex items-center space-x-4">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={() => handleSelectOrder(order.id)}
                          className="rounded"
                        />

                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium">#{order.orderNumber}</h4>
                            <Badge className={PRIORITY_LEVELS[order.priority].color}>
                              {PRIORITY_LEVELS[order.priority].label}
                            </Badge>
                            <Badge variant="outline">{order.status}</Badge>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                            <div>
                              <span className="font-medium">Customer:</span>
                              <p>{order.customer?.firstName} {order.customer?.lastName}</p>
                            </div>
                            <div>
                              <span className="font-medium">Items:</span>
                              <p>{order.itemCount} items</p>
                            </div>
                            <div>
                              <span className="font-medium">Value:</span>
                              <p>{formatCurrency(order.total?.amount || 0)}</p>
                            </div>
                            <div>
                              <span className="font-medium">Created:</span>
                              <p>{formatDate(order.createdAt)}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {onProcessOrder && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onProcessOrder(order)}
                            >
                              Process
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Processing Time Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Processing Time Analysis</CardTitle>
                <CardDescription>Average time spent in each stage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {PROCESSING_STAGES.map((stage, index) => {
                    // Mock processing times (in hours)
                    const avgTimes = [0.5, 1.0, 2.0, 1.5, 0.5, 0.2]
                    const avgTime = avgTimes[index] || 1.0
                    const maxTime = Math.max(...avgTimes)
                    const percentage = (avgTime / maxTime) * 100

                    return (
                      <div key={stage.id}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">{stage.label}</span>
                          <span className="text-sm text-muted-foreground">{avgTime}h avg</span>
                        </div>
                        <Progress value={percentage} className="w-full" />
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Order Volume Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Order Volume Trends</CardTitle>
                <CardDescription>Daily order processing volume</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Mock daily data for the last 7 days */}
                  {Array.from({ length: 7 }, (_, i) => {
                    const date = new Date()
                    date.setDate(date.getDate() - (6 - i))
                    const dayOrders = Math.floor(Math.random() * 50) + 20
                    const maxOrders = 70
                    const percentage = (dayOrders / maxOrders) * 100

                    return (
                      <div key={i}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">
                            {date.toLocaleDateString('en-ZA', { weekday: 'short', month: 'short', day: 'numeric' })}
                          </span>
                          <span className="text-sm text-muted-foreground">{dayOrders} orders</span>
                        </div>
                        <Progress value={percentage} className="w-full" />
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Error Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Error Analysis</CardTitle>
                <CardDescription>Common processing issues and their frequency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { type: 'Inventory Shortage', count: 3, percentage: 40 },
                    { type: 'Address Validation', count: 2, percentage: 27 },
                    { type: 'Payment Issues', count: 2, percentage: 27 },
                    { type: 'System Errors', count: 1, percentage: 13 },
                  ].map((error, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{error.type}</span>
                        <span className="text-sm text-muted-foreground">{error.count} issues</span>
                      </div>
                      <Progress value={error.percentage} className="w-full" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Staff Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Staff Performance</CardTitle>
                <CardDescription>Processing efficiency by team member</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: 'Sarah Johnson', processed: 45, efficiency: 95 },
                    { name: 'Mike Chen', processed: 38, efficiency: 92 },
                    { name: 'Lisa Williams', processed: 42, efficiency: 88 },
                    { name: 'David Brown', processed: 35, efficiency: 85 },
                  ].map((staff, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <p className="font-medium">{staff.name}</p>
                        <p className="text-sm text-muted-foreground">{staff.processed} orders processed</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{staff.efficiency}%</p>
                        <p className="text-sm text-muted-foreground">Efficiency</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Alerts and Notifications */}
      {processingQueue.filter(order => order.priority === 'urgent').length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Urgent Orders Require Attention</AlertTitle>
          <AlertDescription>
            {processingQueue.filter(order => order.priority === 'urgent').length} orders have been waiting for over 24 hours and require immediate processing.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
