'use client'

import { useState, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Minus, 
  Plus,
  Search,
  Filter,
  BarChart3,
  TrendingDown,
  TrendingUp,
  RefreshCw,
  Download,
  Upload,
  <PERSON>an,
  MapPin,
  Clock,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order, OrderItem } from '@/lib/ecommerce/types'

// Inventory adjustment schema
const inventoryAdjustmentSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  variantId: z.string().optional(),
  locationId: z.string().min(1, 'Location is required'),
  adjustmentType: z.enum(['increase', 'decrease', 'set']),
  quantity: z.number().min(0, 'Quantity cannot be negative'),
  reason: z.enum(['recount', 'damage', 'theft', 'return', 'correction', 'other']),
  notes: z.string().max(500, 'Notes are too long').optional(),
})

type InventoryAdjustmentFormData = z.infer<typeof inventoryAdjustmentSchema>

interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  productTitle: string
  variantTitle?: string
  locationId: string
  locationName: string
  quantityAvailable: number
  quantityReserved: number
  quantityOnHand: number
  reorderPoint: number
  maxStock: number
  lastUpdated: Date
  binLocation?: string
}

interface OrderInventoryManagerProps {
  orders: Order[]
  onInventoryUpdate?: (item: InventoryItem) => void
  onReservationUpdate?: (orderId: string, items: any[]) => void
}

// Mock inventory data
const mockInventoryItems: InventoryItem[] = [
  {
    id: '1',
    productId: 'prod-1',
    sku: 'COCO-001',
    productTitle: 'Coconut Milk Powder',
    locationId: 'warehouse-1',
    locationName: 'Main Warehouse',
    quantityAvailable: 150,
    quantityReserved: 25,
    quantityOnHand: 175,
    reorderPoint: 50,
    maxStock: 500,
    lastUpdated: new Date(),
    binLocation: 'A1-B2-C3',
  },
  {
    id: '2',
    productId: 'prod-2',
    sku: 'COCO-002',
    productTitle: 'Organic Coconut Oil',
    locationId: 'warehouse-1',
    locationName: 'Main Warehouse',
    quantityAvailable: 75,
    quantityReserved: 15,
    quantityOnHand: 90,
    reorderPoint: 30,
    maxStock: 200,
    lastUpdated: new Date(),
    binLocation: 'B1-C2-D3',
  },
  // Add more mock items as needed
]

const inventoryLocations = [
  { id: 'warehouse-1', name: 'Main Warehouse', address: 'Cape Town, WC' },
  { id: 'warehouse-2', name: 'Johannesburg Hub', address: 'Johannesburg, GP' },
  { id: 'store-1', name: 'V&A Waterfront Store', address: 'Cape Town, WC' },
]

export function OrderInventoryManager({ orders, onInventoryUpdate, onReservationUpdate }: OrderInventoryManagerProps) {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>(mockInventoryItems)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLocation, setSelectedLocation] = useState<string>('all')
  const [stockFilter, setStockFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<InventoryAdjustmentFormData>({
    resolver: zodResolver(inventoryAdjustmentSchema),
    mode: 'onChange',
    defaultValues: {
      adjustmentType: 'set',
      quantity: 0,
      reason: 'correction',
    },
  })

  // Calculate inventory metrics
  const inventoryMetrics = useMemo(() => {
    const totalItems = inventoryItems.length
    const lowStockItems = inventoryItems.filter(item => 
      item.quantityAvailable <= item.reorderPoint
    ).length
    const outOfStockItems = inventoryItems.filter(item => 
      item.quantityAvailable === 0
    ).length
    const overStockItems = inventoryItems.filter(item => 
      item.quantityOnHand > item.maxStock
    ).length
    
    const totalValue = inventoryItems.reduce((sum, item) => 
      sum + (item.quantityOnHand * 50), 0 // Mock price of R50 per item
    )
    
    const reservedQuantity = inventoryItems.reduce((sum, item) => 
      sum + item.quantityReserved, 0
    )

    return {
      totalItems,
      lowStockItems,
      outOfStockItems,
      overStockItems,
      totalValue,
      reservedQuantity,
    }
  }, [inventoryItems])

  // Filter inventory items
  const filteredItems = useMemo(() => {
    let filtered = inventoryItems

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(item => 
        item.sku.toLowerCase().includes(searchLower) ||
        item.productTitle.toLowerCase().includes(searchLower) ||
        item.variantTitle?.toLowerCase().includes(searchLower)
      )
    }

    // Location filter
    if (selectedLocation !== 'all') {
      filtered = filtered.filter(item => item.locationId === selectedLocation)
    }

    // Stock level filter
    if (stockFilter !== 'all') {
      switch (stockFilter) {
        case 'low':
          filtered = filtered.filter(item => 
            item.quantityAvailable <= item.reorderPoint && item.quantityAvailable > 0
          )
          break
        case 'out':
          filtered = filtered.filter(item => item.quantityAvailable === 0)
          break
        case 'overstock':
          filtered = filtered.filter(item => item.quantityOnHand > item.maxStock)
          break
      }
    }

    return filtered
  }, [inventoryItems, searchTerm, selectedLocation, stockFilter])

  // Get stock status for an item
  const getStockStatus = (item: InventoryItem) => {
    if (item.quantityAvailable === 0) {
      return { status: 'out', label: 'Out of Stock', color: 'bg-red-100 text-red-800' }
    } else if (item.quantityAvailable <= item.reorderPoint) {
      return { status: 'low', label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' }
    } else if (item.quantityOnHand > item.maxStock) {
      return { status: 'over', label: 'Overstock', color: 'bg-purple-100 text-purple-800' }
    } else {
      return { status: 'good', label: 'In Stock', color: 'bg-green-100 text-green-800' }
    }
  }

  // Calculate stock level percentage
  const getStockPercentage = (item: InventoryItem) => {
    return Math.min((item.quantityAvailable / item.maxStock) * 100, 100)
  }

  // Handle inventory adjustment
  const handleInventoryAdjustment = async (data: InventoryAdjustmentFormData) => {
    try {
      setLoading(true)
      
      console.log('Processing inventory adjustment:', data)

      // Find the item to adjust
      const itemIndex = inventoryItems.findIndex(item => 
        item.productId === data.productId && 
        item.variantId === data.variantId &&
        item.locationId === data.locationId
      )

      if (itemIndex === -1) {
        toast.error('Inventory item not found')
        return
      }

      const item = inventoryItems[itemIndex]
      let newQuantity = item.quantityOnHand

      switch (data.adjustmentType) {
        case 'increase':
          newQuantity = item.quantityOnHand + data.quantity
          break
        case 'decrease':
          newQuantity = Math.max(0, item.quantityOnHand - data.quantity)
          break
        case 'set':
          newQuantity = data.quantity
          break
      }

      // Update the item
      const updatedItem = {
        ...item,
        quantityOnHand: newQuantity,
        quantityAvailable: Math.max(0, newQuantity - item.quantityReserved),
        lastUpdated: new Date(),
      }

      // Update state
      const newItems = [...inventoryItems]
      newItems[itemIndex] = updatedItem
      setInventoryItems(newItems)

      toast.success(`Inventory updated for ${item.sku}`)
      
      if (onInventoryUpdate) {
        onInventoryUpdate(updatedItem)
      }

      reset()
    } catch (error) {
      console.error('Error updating inventory:', error)
      toast.error('Failed to update inventory')
    } finally {
      setLoading(false)
    }
  }

  // Reserve inventory for orders
  const reserveInventoryForOrder = async (order: Order) => {
    try {
      setLoading(true)
      
      const reservations: any[] = []
      let canFulfill = true

      // Check availability for each item
      for (const orderItem of order.items || []) {
        const inventoryItem = inventoryItems.find(item => 
          item.productId === orderItem.productId &&
          item.variantId === orderItem.variantId
        )

        if (!inventoryItem) {
          toast.error(`Product ${orderItem.productTitle} not found in inventory`)
          canFulfill = false
          break
        }

        if (inventoryItem.quantityAvailable < orderItem.quantity) {
          toast.error(`Insufficient stock for ${orderItem.productTitle}`)
          canFulfill = false
          break
        }

        reservations.push({
          orderItemId: orderItem.id,
          inventoryItemId: inventoryItem.id,
          quantity: orderItem.quantity,
        })
      }

      if (!canFulfill) {
        return
      }

      // Apply reservations
      const newItems = inventoryItems.map(item => {
        const reservation = reservations.find(r => r.inventoryItemId === item.id)
        if (reservation) {
          return {
            ...item,
            quantityReserved: item.quantityReserved + reservation.quantity,
            quantityAvailable: item.quantityAvailable - reservation.quantity,
          }
        }
        return item
      })

      setInventoryItems(newItems)
      toast.success(`Inventory reserved for order #${order.orderNumber}`)

      if (onReservationUpdate) {
        onReservationUpdate(order.id, reservations)
      }
    } catch (error) {
      console.error('Error reserving inventory:', error)
      toast.error('Failed to reserve inventory')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">
            Monitor and manage inventory levels for order processing
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Sync
          </Button>
        </div>
      </div>

      {/* Inventory Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryMetrics.totalItems}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{inventoryMetrics.lowStockItems}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{inventoryMetrics.outOfStockItems}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overstock</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{inventoryMetrics.overStockItems}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reserved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryMetrics.reservedQuantity}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(inventoryMetrics.totalValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {inventoryMetrics.outOfStockItems > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Out of Stock Items</AlertTitle>
          <AlertDescription>
            {inventoryMetrics.outOfStockItems} items are out of stock and may affect order fulfillment.
          </AlertDescription>
        </Alert>
      )}

      {inventoryMetrics.lowStockItems > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Low Stock Warning</AlertTitle>
          <AlertDescription>
            {inventoryMetrics.lowStockItems} items are running low and should be restocked soon.
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search SKU or product..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Location</Label>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {inventoryLocations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Stock Level</Label>
              <Select value={stockFilter} onValueChange={setStockFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Items</SelectItem>
                  <SelectItem value="low">Low Stock</SelectItem>
                  <SelectItem value="out">Out of Stock</SelectItem>
                  <SelectItem value="overstock">Overstock</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('')
                  setSelectedLocation('all')
                  setStockFilter('all')
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Inventory Overview</TabsTrigger>
          <TabsTrigger value="adjustments">Adjustments</TabsTrigger>
          <TabsTrigger value="reservations">Reservations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Items ({filteredItems.length})</CardTitle>
              <CardDescription>
                Current inventory levels across all locations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredItems.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold text-muted-foreground">No items found</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Try adjusting your search or filters
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredItems.map((item) => {
                    const stockStatus = getStockStatus(item)
                    const stockPercentage = getStockPercentage(item)

                    return (
                      <div key={item.id} className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-medium">{item.productTitle}</h4>
                            {item.variantTitle && (
                              <p className="text-sm text-muted-foreground">{item.variantTitle}</p>
                            )}
                            <p className="text-sm text-muted-foreground">
                              SKU: {item.sku} | Location: {item.locationName}
                              {item.binLocation && ` (${item.binLocation})`}
                            </p>
                          </div>
                          <Badge className={stockStatus.color}>
                            {stockStatus.label}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-3">
                          <div>
                            <p className="text-sm text-muted-foreground">Available</p>
                            <p className="text-lg font-semibold">{item.quantityAvailable}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Reserved</p>
                            <p className="text-lg font-semibold">{item.quantityReserved}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">On Hand</p>
                            <p className="text-lg font-semibold">{item.quantityOnHand}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Reorder Point</p>
                            <p className="text-lg font-semibold">{item.reorderPoint}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Max Stock</p>
                            <p className="text-lg font-semibold">{item.maxStock}</p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Stock Level</span>
                            <span className="text-sm text-muted-foreground">
                              {stockPercentage.toFixed(0)}% of max capacity
                            </span>
                          </div>
                          <Progress value={stockPercentage} className="w-full" />
                        </div>

                        <div className="flex items-center justify-between mt-3 pt-3 border-t">
                          <p className="text-xs text-muted-foreground">
                            Last updated: {item.lastUpdated.toLocaleDateString()}
                          </p>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setValue('productId', item.productId)
                                setValue('variantId', item.variantId || '')
                                setValue('locationId', item.locationId)
                                setActiveTab('adjustments')
                              }}
                            >
                              Adjust
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="adjustments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Adjustments</CardTitle>
              <CardDescription>
                Manually adjust inventory quantities for corrections, damages, or restocking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(handleInventoryAdjustment)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="productId">Product ID *</Label>
                    <Input
                      id="productId"
                      {...register('productId')}
                      placeholder="Enter product ID"
                    />
                    {errors.productId && (
                      <p className="text-sm text-red-500">{errors.productId.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="variantId">Variant ID</Label>
                    <Input
                      id="variantId"
                      {...register('variantId')}
                      placeholder="Enter variant ID (optional)"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="locationId">Location *</Label>
                    <Select
                      value={watch('locationId')}
                      onValueChange={(value) => setValue('locationId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        {inventoryLocations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.locationId && (
                      <p className="text-sm text-red-500">{errors.locationId.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="adjustmentType">Adjustment Type *</Label>
                    <Select
                      value={watch('adjustmentType')}
                      onValueChange={(value: any) => setValue('adjustmentType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="increase">Increase Quantity</SelectItem>
                        <SelectItem value="decrease">Decrease Quantity</SelectItem>
                        <SelectItem value="set">Set Exact Quantity</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">Quantity *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="0"
                      {...register('quantity', { valueAsNumber: true })}
                      placeholder="Enter quantity"
                    />
                    {errors.quantity && (
                      <p className="text-sm text-red-500">{errors.quantity.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reason">Reason *</Label>
                    <Select
                      value={watch('reason')}
                      onValueChange={(value: any) => setValue('reason', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="recount">Physical Recount</SelectItem>
                        <SelectItem value="damage">Damaged Goods</SelectItem>
                        <SelectItem value="theft">Theft/Loss</SelectItem>
                        <SelectItem value="return">Customer Return</SelectItem>
                        <SelectItem value="correction">System Correction</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Input
                    id="notes"
                    {...register('notes')}
                    placeholder="Additional notes about this adjustment..."
                  />
                  {errors.notes && (
                    <p className="text-sm text-red-500">{errors.notes.message}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    type="submit"
                    disabled={loading || isSubmitting}
                    className="min-w-[120px]"
                  >
                    {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Apply Adjustment
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => reset()}
                  >
                    Clear
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reservations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Reservations</CardTitle>
              <CardDescription>
                Reserve inventory for pending orders to ensure availability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {orders.filter(order => ['pending', 'confirmed'].includes(order.status)).map((order) => (
                  <div key={order.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium">Order #{order.orderNumber}</h4>
                        <p className="text-sm text-muted-foreground">
                          {order.customer?.firstName} {order.customer?.lastName} • {order.itemCount} items
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{order.status}</Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => reserveInventoryForOrder(order)}
                          disabled={loading}
                        >
                          Reserve Inventory
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      {order.items?.map((item, index) => {
                        const inventoryItem = inventoryItems.find(inv =>
                          inv.productId === item.productId && inv.variantId === item.variantId
                        )
                        const canFulfill = inventoryItem && inventoryItem.quantityAvailable >= item.quantity

                        return (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                            <div>
                              <p className="font-medium">{item.productTitle}</p>
                              <p className="text-sm text-muted-foreground">
                                Qty: {item.quantity} | SKU: {item.sku || 'N/A'}
                              </p>
                            </div>
                            <div className="text-right">
                              {inventoryItem ? (
                                <div>
                                  <p className="text-sm">
                                    Available: {inventoryItem.quantityAvailable}
                                  </p>
                                  <Badge
                                    variant={canFulfill ? "default" : "destructive"}
                                    className="text-xs"
                                  >
                                    {canFulfill ? 'Can Fulfill' : 'Insufficient Stock'}
                                  </Badge>
                                </div>
                              ) : (
                                <Badge variant="destructive" className="text-xs">
                                  Not Found
                                </Badge>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}

                {orders.filter(order => ['pending', 'confirmed'].includes(order.status)).length === 0 && (
                  <div className="text-center py-8">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <h3 className="mt-2 text-sm font-semibold text-green-800">No pending orders</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      All orders have been processed or reserved
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
