'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Save, Loader2, Package, Truck, CheckCircle, XCircle, AlertTriangle, Clock } from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types'

// Status update validation schema
const statusUpdateSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded', 'returned']),
  financialStatus: z.enum(['pending', 'authorized', 'partially_paid', 'paid', 'partially_refunded', 'refunded', 'voided']),
  fulfillmentStatus: z.enum(['unfulfilled', 'partially_fulfilled', 'fulfilled', 'shipped', 'delivered', 'returned', 'cancelled']),
  trackingNumber: z.string().max(100, 'Tracking number is too long').optional(),
  trackingUrl: z.string().url('Invalid tracking URL').optional().or(z.literal('')),
  reason: z.string().max(500, 'Reason is too long').optional(),
  notifyCustomer: z.boolean().default(true),
  internalNote: z.string().max(1000, 'Internal note is too long').optional(),
  refundAmount: z.number().min(0, 'Refund amount cannot be negative').optional(),
  estimatedDelivery: z.string().optional(), // Date string
})

type StatusUpdateFormData = z.infer<typeof statusUpdateSchema>

interface OrderStatusManagerProps {
  order: Order
  onStatusUpdate?: (order: Order) => void
  onClose?: () => void
}

// Status configuration with colors and icons
const statusConfig = {
  pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
  confirmed: { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, label: 'Confirmed' },
  processing: { color: 'bg-purple-100 text-purple-800', icon: Package, label: 'Processing' },
  shipped: { color: 'bg-indigo-100 text-indigo-800', icon: Truck, label: 'Shipped' },
  delivered: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Delivered' },
  cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Cancelled' },
  refunded: { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle, label: 'Refunded' },
  returned: { color: 'bg-gray-100 text-gray-800', icon: Package, label: 'Returned' },
}

const financialStatusConfig = {
  pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
  authorized: { color: 'bg-blue-100 text-blue-800', label: 'Authorized' },
  partially_paid: { color: 'bg-orange-100 text-orange-800', label: 'Partially Paid' },
  paid: { color: 'bg-green-100 text-green-800', label: 'Paid' },
  partially_refunded: { color: 'bg-yellow-100 text-yellow-800', label: 'Partially Refunded' },
  refunded: { color: 'bg-red-100 text-red-800', label: 'Refunded' },
  voided: { color: 'bg-gray-100 text-gray-800', label: 'Voided' },
}

const fulfillmentStatusConfig = {
  unfulfilled: { color: 'bg-yellow-100 text-yellow-800', label: 'Unfulfilled' },
  partially_fulfilled: { color: 'bg-orange-100 text-orange-800', label: 'Partially Fulfilled' },
  fulfilled: { color: 'bg-blue-100 text-blue-800', label: 'Fulfilled' },
  shipped: { color: 'bg-indigo-100 text-indigo-800', label: 'Shipped' },
  delivered: { color: 'bg-green-100 text-green-800', label: 'Delivered' },
  returned: { color: 'bg-gray-100 text-gray-800', label: 'Returned' },
  cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
}

export function OrderStatusManager({ order, onStatusUpdate, onClose }: OrderStatusManagerProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<StatusUpdateFormData>({
    resolver: zodResolver(statusUpdateSchema),
    mode: 'onChange',
    defaultValues: {
      status: order.status,
      financialStatus: order.financialStatus,
      fulfillmentStatus: order.fulfillmentStatus,
      trackingNumber: order.shippingMethod?.trackingNumber || '',
      trackingUrl: order.shippingMethod?.trackingUrl || '',
      reason: '',
      notifyCustomer: true,
      internalNote: '',
      refundAmount: 0,
      estimatedDelivery: order.shippingMethod?.estimatedDelivery 
        ? new Date(order.shippingMethod.estimatedDelivery).toISOString().split('T')[0] 
        : '',
    },
  })

  const watchedStatus = watch('status')
  const watchedFinancialStatus = watch('financialStatus')
  const watchedFulfillmentStatus = watch('fulfillmentStatus')

  // Function to show validation errors as toasts
  const showValidationErrors = () => {
    if (Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, error]) => {
        if (error?.message) {
          const fieldName = field
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
          toast.error(`${fieldName}: ${error.message}`)
        }
      })
      return true
    }
    return false
  }

  const handleStatusUpdate = async (data: StatusUpdateFormData) => {
    console.log('Status update started with data:', data)
    
    try {
      setLoading(true)
      setError(null)

      // Check for validation errors
      if (showValidationErrors()) {
        toast.error('Please fix the validation errors before submitting')
        return
      }

      // Prepare update data
      const updateData = {
        id: order.id,
        status: data.status,
        financialStatus: data.financialStatus,
        fulfillmentStatus: data.fulfillmentStatus,
        ...(data.trackingNumber && { trackingNumber: data.trackingNumber }),
        ...(data.trackingUrl && { trackingUrl: data.trackingUrl }),
        ...(data.reason && { reason: data.reason }),
        ...(data.internalNote && { internalNote: data.internalNote }),
        ...(data.refundAmount && data.refundAmount > 0 && { refundAmount: data.refundAmount }),
        ...(data.estimatedDelivery && { estimatedDelivery: data.estimatedDelivery }),
        notifyCustomer: data.notifyCustomer,
        isAdmin: true, // Mark as admin update
      }

      console.log('Sending status update:', updateData)

      // Call API to update order status
      const response = await fetch(`/api/e-commerce/orders/${order.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true',
        },
        body: JSON.stringify(updateData),
      })

      const result = await response.json()

      if (result.success && result.data) {
        toast.success(`Order #${order.orderNumber} status updated successfully!`)
        
        if (onStatusUpdate) {
          onStatusUpdate(result.data)
        }
        
        if (onClose) {
          onClose()
        }
      } else {
        setError(result.error || 'Failed to update order status')
        toast.error(result.error || 'Failed to update order status')
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setError(errorMessage)
      toast.error(`Failed to update order status: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const currentStatusConfig = statusConfig[watchedStatus]
  const CurrentStatusIcon = currentStatusConfig?.icon || Clock

  return (
    <form onSubmit={handleSubmit(handleStatusUpdate)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Update Order Status
          </h2>
          <p className="text-muted-foreground">
            Order #{order.orderNumber} - {order.customer?.email}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {onClose && (
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting || loading}>
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={loading || isSubmitting}
            className="min-w-[120px]"
          >
            {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            Update Status
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Current Status Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CurrentStatusIcon className="mr-2 h-5 w-5" />
            Current Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Order Status</Label>
              <Badge className={statusConfig[order.status]?.color}>
                {statusConfig[order.status]?.label}
              </Badge>
            </div>
            <div className="space-y-2">
              <Label>Financial Status</Label>
              <Badge className={financialStatusConfig[order.financialStatus]?.color}>
                {financialStatusConfig[order.financialStatus]?.label}
              </Badge>
            </div>
            <div className="space-y-2">
              <Label>Fulfillment Status</Label>
              <Badge className={fulfillmentStatusConfig[order.fulfillmentStatus]?.color}>
                {fulfillmentStatusConfig[order.fulfillmentStatus]?.label}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Updates */}
      <Card>
        <CardHeader>
          <CardTitle>Update Status</CardTitle>
          <CardDescription>
            Change the order status and add relevant information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Order Status</Label>
              <Select
                value={watchedStatus}
                onValueChange={(value: any) => setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="returned">Returned</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-500">{errors.status.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="financialStatus">Financial Status</Label>
              <Select
                value={watchedFinancialStatus}
                onValueChange={(value: any) => setValue('financialStatus', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="authorized">Authorized</SelectItem>
                  <SelectItem value="partially_paid">Partially Paid</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="partially_refunded">Partially Refunded</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="voided">Voided</SelectItem>
                </SelectContent>
              </Select>
              {errors.financialStatus && (
                <p className="text-sm text-red-500">{errors.financialStatus.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="fulfillmentStatus">Fulfillment Status</Label>
              <Select
                value={watchedFulfillmentStatus}
                onValueChange={(value: any) => setValue('fulfillmentStatus', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unfulfilled">Unfulfilled</SelectItem>
                  <SelectItem value="partially_fulfilled">Partially Fulfilled</SelectItem>
                  <SelectItem value="fulfilled">Fulfilled</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="returned">Returned</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              {errors.fulfillmentStatus && (
                <p className="text-sm text-red-500">{errors.fulfillmentStatus.message}</p>
              )}
            </div>
          </div>

          {/* Shipping Information (shown when status is shipped or delivered) */}
          {(watchedStatus === 'shipped' || watchedStatus === 'delivered' || watchedFulfillmentStatus === 'shipped' || watchedFulfillmentStatus === 'delivered') && (
            <>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingNumber">Tracking Number</Label>
                  <Input
                    id="trackingNumber"
                    {...register('trackingNumber')}
                    placeholder="Enter tracking number"
                  />
                  {errors.trackingNumber && (
                    <p className="text-sm text-red-500">{errors.trackingNumber.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trackingUrl">Tracking URL</Label>
                  <Input
                    id="trackingUrl"
                    type="url"
                    {...register('trackingUrl')}
                    placeholder="https://tracking.example.com/..."
                  />
                  {errors.trackingUrl && (
                    <p className="text-sm text-red-500">{errors.trackingUrl.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedDelivery">Estimated Delivery Date</Label>
                <Input
                  id="estimatedDelivery"
                  type="date"
                  {...register('estimatedDelivery')}
                />
              </div>
            </>
          )}

          {/* Refund Information (shown when status is refunded) */}
          {(watchedStatus === 'refunded' || watchedFinancialStatus === 'refunded' || watchedFinancialStatus === 'partially_refunded') && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label htmlFor="refundAmount">Refund Amount (ZAR)</Label>
                <Input
                  id="refundAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  {...register('refundAmount', { valueAsNumber: true })}
                  placeholder="0.00"
                />
                {errors.refundAmount && (
                  <p className="text-sm text-red-500">{errors.refundAmount.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Order total: R {order.total?.amount?.toFixed(2) || '0.00'}
                </p>
              </div>
            </>
          )}

          {/* Reason (shown for cancelled, refunded, or returned orders) */}
          {(watchedStatus === 'cancelled' || watchedStatus === 'refunded' || watchedStatus === 'returned') && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label htmlFor="reason">Reason</Label>
                <Textarea
                  id="reason"
                  {...register('reason')}
                  placeholder="Explain the reason for this status change..."
                  rows={3}
                />
                {errors.reason && (
                  <p className="text-sm text-red-500">{errors.reason.message}</p>
                )}
              </div>
            </>
          )}

          <Separator />

          {/* Internal Note */}
          <div className="space-y-2">
            <Label htmlFor="internalNote">Internal Note</Label>
            <Textarea
              id="internalNote"
              {...register('internalNote')}
              placeholder="Add an internal note about this status change..."
              rows={3}
            />
            {errors.internalNote && (
              <p className="text-sm text-red-500">{errors.internalNote.message}</p>
            )}
          </div>

          {/* Notify Customer */}
          <div className="flex items-center space-x-2">
            <Switch
              id="notifyCustomer"
              checked={watch('notifyCustomer')}
              onCheckedChange={(checked) => setValue('notifyCustomer', checked)}
            />
            <Label htmlFor="notifyCustomer">Notify customer via email</Label>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
