'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  Layout, 
  FileCode, 
  Layers, 
  Globe,
  Wand2,
  ArrowRight,
  Zap,
  Palette,
  Code,
  Database
} from 'lucide-react'
import { 
  AIVisualEditorLayout, 
  NextJSLayoutGenerator, 
  ComponentLibrary 
} from '@/lib/ai-visual-editor'

export default function AIBuildersPage() {
  const [activeBuilder, setActiveBuilder] = useState<string | null>(null)

  const builders = [
    {
      id: 'visual-editor',
      title: 'AI Visual Editor',
      description: 'Generate React components with AI assistance and dynamic properties',
      icon: Sparkles,
      color: 'bg-blue-500',
      theme: 'builder-theme-visual',
      features: ['Component Generation', 'Properties Panel', 'Live Preview', 'Export Code'],
      status: 'ready'
    },
    {
      id: 'nextjs-generator',
      title: 'Next.js Generator',
      description: 'Create complete Next.js layouts and pages with AI',
      icon: Globe,
      color: 'bg-green-500',
      theme: 'builder-theme-nextjs',
      features: ['Layout Generation', 'Page Creation', 'Route Handling', 'SEO Optimization'],
      status: 'ready'
    },
    {
      id: 'component-library',
      title: 'Component Library',
      description: 'Browse and use pre-built component templates',
      icon: Layers,
      color: 'bg-purple-500',
      features: ['Template Gallery', 'Search & Filter', 'Usage Analytics', 'Community Sharing'],
      status: 'ready'
    },
    {
      id: 'layout-builder',
      title: 'AI Layout Builder',
      description: 'Design complex layouts with AI assistance',
      icon: Layout,
      color: 'bg-orange-500',
      features: ['Grid Systems', 'Responsive Design', 'Component Placement', 'Export Options'],
      status: 'beta'
    },
    {
      id: 'page-builder',
      title: 'AI Page Builder',
      description: 'Build complete pages with drag-and-drop and AI',
      icon: FileCode,
      color: 'bg-indigo-500',
      theme: 'builder-theme-page',
      features: ['Drag & Drop', 'AI Suggestions', 'Content Management', 'SEO Tools'],
      status: 'beta'
    },
    {
      id: 'theme-generator',
      title: 'AI Theme Generator',
      description: 'Generate complete design systems and themes',
      icon: Palette,
      color: 'bg-pink-500',
      theme: 'builder-theme-theme',
      features: ['Color Palettes', 'Typography', 'Component Variants', 'Design Tokens'],
      status: 'coming-soon'
    }
  ]

  const renderBuilderInterface = () => {
    switch (activeBuilder) {
      case 'visual-editor':
        return <AIVisualEditorLayout />
      case 'nextjs-generator':
        return <NextJSLayoutGenerator />
      case 'component-library':
        return <ComponentLibrary />
      default:
        return null
    }
  }

  if (activeBuilder) {
    const currentBuilder = builders.find(b => b.id === activeBuilder)
    return (
      <div className={`h-screen flex flex-col ${currentBuilder?.theme || 'builder-theme-visual'}`}>
        <div className="builder-toolbar-height bg-background border-b border-border flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveBuilder(null)}
              className="builder-button-ghost"
            >
              ← Back to Builders
            </Button>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-current/10 rounded-lg flex items-center justify-center">
                {(() => {
                  const Icon = currentBuilder?.icon
                  return Icon ? <Icon className="w-5 h-5 text-current" /> : null
                })()}
              </div>
              <div>
                <h1 className="text-lg font-semibold text-foreground">
                  {currentBuilder?.title}
                </h1>
                <p className="text-xs text-muted-foreground">
                  {currentBuilder?.description}
                </p>
              </div>
            </div>
          </div>
          <Badge className={`${
            currentBuilder?.status === 'ready' ? 'builder-status-ready' :
            currentBuilder?.status === 'beta' ? 'builder-status-beta' :
            'builder-status-coming-soon'
          }`}>
            {currentBuilder?.status === 'ready' ? 'Ready' :
             currentBuilder?.status === 'beta' ? 'Beta' : 'Coming Soon'}
          </Badge>
        </div>
        <div className="flex-1 overflow-hidden">
          {renderBuilderInterface()}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header */}
      <div className="bg-background border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <div className="flex items-center space-x-4 mb-6 builder-slide-in">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                <Wand2 className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">AI Builders Suite</h1>
                <p className="text-muted-foreground text-lg">
                  Powerful AI-driven tools for building modern web applications
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-8 text-sm builder-slide-up">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-muted-foreground">3 Ready</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-muted-foreground">2 Beta</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span className="text-muted-foreground">1 Coming Soon</span>
              </div>
              <div className="flex items-center space-x-2 ml-auto">
                <div className="w-2 h-2 bg-green-500 rounded-full builder-pulse"></div>
                <span className="text-muted-foreground">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="all" className="space-y-8">
          <TabsList className="grid w-full grid-cols-4 h-auto p-1 bg-muted/30">
            <TabsTrigger
              value="all"
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              All Builders
            </TabsTrigger>
            <TabsTrigger
              value="ready"
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              Ready
            </TabsTrigger>
            <TabsTrigger
              value="beta"
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              Beta
            </TabsTrigger>
            <TabsTrigger
              value="coming-soon"
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
            >
              Coming Soon
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6 builder-slide-up">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.map((builder, index) => (
                <div key={builder.id} style={{ animationDelay: `${index * 100}ms` }} className="builder-slide-up">
                  <BuilderCard
                    builder={builder}
                    onSelect={() => setActiveBuilder(builder.id)}
                  />
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="ready" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'ready').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => setActiveBuilder(builder.id)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="beta" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'beta').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => setActiveBuilder(builder.id)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="coming-soon" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {builders.filter(b => b.status === 'coming-soon').map((builder) => (
                <BuilderCard
                  key={builder.id}
                  builder={builder}
                  onSelect={() => {}}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Enhanced Quick Start Section */}
        <div className="mt-16">
          <div className="text-center mb-8 builder-slide-up">
            <h2 className="text-2xl font-bold text-foreground mb-2">Quick Start</h2>
            <p className="text-muted-foreground">Get started with AI-powered development in minutes</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="builder-panel builder-theme-visual hover:shadow-lg transition-all hover:-translate-y-1">
              <div className="builder-panel-content">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                    <Zap className="w-5 h-5 text-current" />
                  </div>
                  <h3 className="font-semibold text-foreground">Generate Components</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-6">
                  Start with the AI Visual Editor to generate React components from natural language descriptions.
                </p>
                <Button
                  size="sm"
                  onClick={() => setActiveBuilder('visual-editor')}
                  className="w-full builder-button-primary"
                >
                  Start Building
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </Card>

            <Card className="builder-panel builder-theme-nextjs hover:shadow-lg transition-all hover:-translate-y-1">
              <div className="builder-panel-content">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                    <Globe className="w-5 h-5 text-current" />
                  </div>
                  <h3 className="font-semibold text-foreground">Create Pages</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-6">
                  Use the Next.js Generator to create complete layouts and pages with proper routing.
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setActiveBuilder('nextjs-generator')}
                  className="w-full builder-button-secondary"
                >
                  Generate Pages
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </Card>

            <Card className="builder-panel builder-theme-page hover:shadow-lg transition-all hover:-translate-y-1">
              <div className="builder-panel-content">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                    <Layers className="w-5 h-5 text-current" />
                  </div>
                  <h3 className="font-semibold text-foreground">Browse Templates</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-6">
                  Explore the component library for pre-built templates and community contributions.
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setActiveBuilder('component-library')}
                  className="w-full builder-button-secondary"
                >
                  Browse Library
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

function BuilderCard({
  builder,
  onSelect
}: {
  builder: any
  onSelect: () => void
}) {
  const Icon = builder.icon
  const isDisabled = builder.status === 'coming-soon'

  return (
    <Card className={`builder-panel ${builder.theme} transition-all hover:shadow-lg hover:-translate-y-1 ${
      isDisabled ? 'opacity-60' : 'cursor-pointer hover:border-current/30'
    }`}>
      <div className="builder-panel-content">
        <div className="flex items-start justify-between mb-4">
          <div className="w-12 h-12 bg-current/10 rounded-xl flex items-center justify-center">
            <Icon className="w-6 h-6 text-current" />
          </div>
          <Badge
            className={`text-xs ${
              builder.status === 'ready' ? 'builder-status-ready' :
              builder.status === 'beta' ? 'builder-status-beta' :
              'builder-status-coming-soon'
            }`}
          >
            {builder.status === 'ready' ? 'Ready' : builder.status === 'beta' ? 'Beta' : 'Coming Soon'}
          </Badge>
        </div>

        <h3 className="text-lg font-semibold text-foreground mb-2">{builder.title}</h3>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{builder.description}</p>

        <div className="space-y-2 mb-6">
          {builder.features.slice(0, 3).map((feature: string, index: number) => (
            <div key={index} className="flex items-center space-x-2 text-xs text-muted-foreground">
              <div className="w-1.5 h-1.5 bg-current rounded-full opacity-60"></div>
              <span>{feature}</span>
            </div>
          ))}
          {builder.features.length > 3 && (
            <div className="text-xs text-muted-foreground/60">
              +{builder.features.length - 3} more features
            </div>
          )}
        </div>

        <Button
          onClick={onSelect}
          disabled={isDisabled}
          className={`w-full ${
            isDisabled ? 'builder-button-secondary' : 'builder-button-primary'
          }`}
          variant={isDisabled ? 'outline' : 'default'}
        >
          {isDisabled ? 'Coming Soon' : 'Open Builder'}
          {!isDisabled && <ArrowRight className="w-4 h-4 ml-2" />}
        </Button>
      </div>
    </Card>
  )
}
