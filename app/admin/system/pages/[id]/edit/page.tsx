"use client";

import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEditorStore } from "@/lib/ai-visual-editor/stores/editor-store";
import { PropertiesPanel } from "@/lib/ai-visual-editor/components/properties-panel";
import { ComponentTree } from "@/lib/ai-visual-editor/components/component-tree";
import { LivePreview } from "@/lib/ai-visual-editor/components/live-preview";
import { PageData } from "@/lib/page-builder/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  ArrowLeft,
  Save,
  Eye,
  MessageSquare,
  Settings,
  Layers,
  Globe,
  Sparkles,
  Download,
  Share,
  RotateCcw,
  Code,
  Play,
  Monitor,
  Tablet,
  Smartphone,
} from "lucide-react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { AiChatPanel } from "@/lib/ai-visual-editor/components/ai-chat-panel";


export default function AIEnhancedPageEditor() {
  const params = useParams();
  const router = useRouter();
  const {
    components,
    selectedComponentId,
    isGenerating,
    isAIResponding,
    clearChat,
    previewMode,
    setPreviewMode,
  } = useEditorStore();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [pageData, setPageData] = useState<PageData | null>(null);
  const [activeRightTab, setActiveRightTab] = useState("properties");

  const pageId = params.id as string;
  const selectedComponent = components.find(
    (c) => c.id === selectedComponentId
  );

  // Load page data
  useEffect(() => {
    const fetchPageData = async () => {
      if (!pageId || pageId === "new") {
        // Initialize with a new page
        const newPage = {
          id: "new",
          title: "New AI-Generated Page",
          slug: "new-ai-page",
          description: "",
          status: "draft" as const,
          type: "custom" as const,
          blocks: [],
          settings: {
            title: "New AI-Generated Page",
          },
        };
        setPageData(newPage);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch(`/api/e-commerce/pages/${pageId}`);
        const data = await response.json();

        if (data.success && data.data) {
          setPageData(data.data);
        } else {
          toast.error("Failed to load page");
          router.push("/admin/system/pages");
        }
      } catch (error) {
        console.error("Error loading page:", error);
        toast.error("Failed to load page");
        router.push("/admin/system/pages");
      } finally {
        setLoading(false);
      }
    };

    fetchPageData();
  }, [pageId, router]);

  // Handle save
  const handleSave = async () => {
    if (!pageData) return;

    try {
      setSaving(true);

      const isNewPage = pageData.id === "new";
      const url = isNewPage
        ? "/api/e-commerce/pages"
        : `/api/e-commerce/pages/${pageData.id}`;
      const method = isNewPage ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: pageData.title,
          slug: pageData.slug,
          description: pageData.description,
          status: pageData.status,
          type: pageData.type,
          blocks: pageData.blocks,
          settings: pageData.settings,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // If it was a new page, redirect to the actual page ID
        if (isNewPage && data.data?.id) {
          toast.success("Page created successfully!");
          router.push(`/admin/system/pages/${data.data.id}/ai-editor`);
          return;
        }

        toast.success("Page saved successfully!");
      } else {
        throw new Error(data.error || "Failed to save page");
      }
    } catch (error) {
      console.error("Error saving page:", error);
      toast.error("Failed to save page");
    } finally {
      setSaving(false);
    }
  };

  // Handle preview
  const handlePreview = () => {
    if (pageData?.slug) {
      window.open(`/preview/${pageData.slug}`, "_blank");
    }
  };

  // Handle back to pages
  const handleBack = () => {
    router.push("/admin/system/pages");
  };

  if (loading) {
    return (
      <div className="h-screen flex flex-col bg-background builder-theme-page">
        {/* Enhanced Header Skeleton */}
        <div className="builder-header-height bg-background border-b border-border flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-9 w-24" />
            <div className="flex items-center space-x-3">
              <Skeleton className="h-12 w-12 rounded-xl" />
              <div>
                <Skeleton className="h-6 w-48 mb-2" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Skeleton className="h-8 w-32 rounded-lg" />
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-16" />
          </div>
        </div>

        {/* Enhanced Content Skeleton */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full flex">
            <div className="w-1/4 border-r border-border p-4">
              <Skeleton className="h-full w-full rounded-xl" />
            </div>
            <div className="w-1/2 border-r border-border p-4">
              <Skeleton className="h-full w-full rounded-xl" />
            </div>
            <div className="w-1/4 p-4">
              <Skeleton className="h-full w-full rounded-xl" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background builder-theme-page">
      {/* Enhanced Header with Global Design Tokens */}
      <div className="builder-header-height bg-background border-b border-border flex items-center justify-between px-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-4">
            <SidebarTrigger className="mr-2" />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="builder-button-ghost"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pages
            </Button>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-current/10 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-current" />
            </div>
            <div>
              <div className="text-foreground text-lg font-semibold">
                {pageData ? pageData.title : "AI Page Editor"}
              </div>
              <div className="text-muted-foreground text-sm">
                NextJS Page Builder with AI • {components.length} components
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full builder-pulse"></div>
            <span className="text-muted-foreground text-sm">Live</span>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Enhanced Device Preview Toggle with Global Tokens */}
          <div className="flex items-center bg-muted rounded-lg p-1 border border-border">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode("desktop")}
              className={`h-8 px-3 transition-all builder-tooltip builder-focus-visible ${
                previewMode === "desktop"
                  ? "bg-current text-white shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-background"
              }`}
              data-tooltip="Desktop view"
            >
              <Monitor className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode("tablet")}
              className={`h-8 px-3 transition-all builder-tooltip builder-focus-visible ${
                previewMode === "tablet"
                  ? "bg-current text-white shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-background"
              }`}
              data-tooltip="Tablet view"
            >
              <Tablet className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode("mobile")}
              className={`h-8 px-3 transition-all builder-tooltip builder-focus-visible ${
                previewMode === "mobile"
                  ? "bg-current text-white shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-background"
              }`}
              data-tooltip="Mobile view"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handlePreview}
            disabled={!pageData?.slug}
            className="builder-button-secondary builder-tooltip"
            data-tooltip="Preview page in new tab"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>

          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
            disabled={saving}
            className="builder-button-primary builder-tooltip"
            data-tooltip="Save page changes"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Main Editor Layout */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Enhanced Left Panel - AI Assistant */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
            <div className="h-full builder-panel builder-theme-visual">
              {/* Enhanced AI Assistant Header */}
              <div className="builder-panel-header">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 text-current" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">AI Assistant</h3>
                      <p className="text-xs text-muted-foreground">
                        Generate NextJS components with Shadcn/UI
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {(isGenerating || isAIResponding) && (
                      <Badge
                        variant="outline"
                        className="text-current border-current/30 text-xs bg-current/10 builder-pulse"
                      >
                        <div className="w-2 h-2 bg-current rounded-full mr-1"></div>
                        Working...
                      </Badge>
                    )}
                    <div className="builder-help-indicator" data-tooltip="AI-powered component generation">
                      ?
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Chat Panel */}
              <div className="h-[calc(100%-80px)] builder-slide-in">
                <AiChatPanel />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Enhanced Center Panel - Properties */}
          <ResizablePanel defaultSize={35} minSize={30} maxSize={50}>
            <div className="h-full builder-panel">
              {/* Enhanced Properties Header */}
              <div className="builder-panel-header">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                      <Settings className="w-5 h-5 text-current" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">
                        Dynamic Properties
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        Configure component properties and behavior
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="secondary"
                      className="text-xs builder-status-ready"
                    >
                      {components.length} components
                    </Badge>
                    {selectedComponent && (
                      <Badge
                        variant="outline"
                        className="text-xs border-current/30 text-current bg-current/10"
                      >
                        {selectedComponent.name}
                      </Badge>
                    )}
                    <div className="builder-help-indicator" data-tooltip="Configure component properties">
                      ?
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Properties Content */}
              <div className="h-[calc(100%-80px)] bg-background">
                <Tabs
                  value={activeRightTab}
                  onValueChange={setActiveRightTab}
                  className="h-full"
                >
                  <div className="properties-section-header">
                    <TabsList className="w-full grid grid-cols-3 h-auto p-1 bg-muted/30">
                      <TabsTrigger
                        value="properties"
                        className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
                      >
                        <Settings className="w-4 h-4" />
                        <span className="text-sm">Properties</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="components"
                        className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
                      >
                        <Layers className="w-4 h-4" />
                        <span className="text-sm">Components</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="nextjs"
                        className="flex items-center space-x-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all builder-focus-visible"
                      >
                        <Globe className="w-4 h-4" />
                        <span className="text-sm">Next.js</span>
                        <Badge variant="outline" className="text-[10px] px-1 py-0 ml-1 builder-status-beta">
                          Beta
                        </Badge>
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent
                    value="properties"
                    className="h-[calc(100%-60px)] mt-0 builder-slide-up"
                  >
                    <PropertiesPanel />
                  </TabsContent>

                  <TabsContent
                    value="components"
                    className="h-[calc(100%-60px)] mt-0 builder-slide-up"
                  >
                    <ComponentTree />
                  </TabsContent>

                  <TabsContent
                    value="nextjs"
                    className="h-[calc(100%-60px)] mt-0 builder-slide-up"
                  >
                    <div className="p-6 text-center">
                      <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center mx-auto mb-4">
                        <Globe className="w-8 h-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-semibold text-foreground mb-2">NextJS Layout Generator</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Advanced layout generation with AI assistance
                      </p>
                      <Badge className="builder-status-beta">
                        Coming Soon
                      </Badge>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Enhanced Right Panel - Live Preview */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <div className="h-full builder-panel">
              {/* Enhanced Preview Header */}
              <div className="builder-panel-header">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-current/10 rounded-xl flex items-center justify-center">
                      <Eye className="w-5 h-5 text-current" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">Live Preview</h3>
                      <p className="text-xs text-muted-foreground">
                        Real-time component preview • {previewMode}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 builder-button-ghost builder-tooltip"
                      data-tooltip="Refresh preview"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 builder-button-ghost builder-tooltip"
                      data-tooltip="View source code"
                    >
                      <Code className="w-4 h-4" />
                    </Button>
                    <div className="builder-help-indicator" data-tooltip="Live component preview">
                      ?
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Live Preview Content */}
              <div className="h-[calc(100%-80px)] builder-slide-up">
                <LivePreview />
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
