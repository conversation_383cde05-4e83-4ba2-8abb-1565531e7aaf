'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useEditorStore } from '@/lib/ai-visual-editor/stores/editor-store'
import { DarkAiChatPanel } from '@/lib/ai-visual-editor/components/dark-ai-chat-panel'
import { DarkPropertiesPanel } from '@/lib/ai-visual-editor/components/dark-properties-panel'
import { LivePreview } from '@/lib/ai-visual-editor/components/live-preview'
import { DarkComponentTree } from '@/lib/ai-visual-editor/components/dark-component-tree'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import {
  ArrowLeft,
  MessageSquare,
  Settings,
  Layers,
  Globe,
  Eye,
  RotateCcw,
  Code,
  Monitor,
  Tablet,
  Smartphone,
  Sparkles,
  Download,
  Share
} from 'lucide-react'

export default function AIVisualEditorPage() {
  const router = useRouter()
  const {
    components,
    selectedComponentId,
    isGenerating,
    isAIResponding,
    previewMode,
    setPreviewMode
  } = useEditorStore()
  const [activeRightTab, setActiveRightTab] = useState('properties')

  const selectedComponent = components.find(c => c.id === selectedComponentId)

  const handleBack = () => {
    router.push('/admin/system/pages')
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      {/* Top Header - VS Code Style */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className="text-gray-300 hover:text-white hover:bg-gray-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>

          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>

          <div className="flex items-center space-x-2 text-gray-300">
            <Sparkles className="w-4 h-4 text-purple-400" />
            <span className="text-sm">AI Visual Editor - NextJS Component Builder</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Device Preview Toggle */}
          <div className="flex items-center bg-gray-700 rounded-md p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode('desktop')}
              className={`h-7 px-2 ${previewMode === 'desktop' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`}
            >
              <Monitor className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode('tablet')}
              className={`h-7 px-2 ${previewMode === 'tablet' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`}
            >
              <Tablet className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode('mobile')}
              className={`h-7 px-2 ${previewMode === 'mobile' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`}
            >
              <Smartphone className="h-3 w-3" />
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            disabled={components.length === 0}
            className="bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>

          <Button
            variant="outline"
            size="sm"
            disabled={components.length === 0}
            className="bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600 hover:text-white"
          >
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      {/* Main Editor Layout */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Left Panel - AI Assistant */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
            <div className="h-full bg-gray-800 border-r border-gray-700">
              {/* AI Assistant Header */}
              <div className="p-3 border-b border-gray-700 bg-gray-750">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <h3 className="font-medium text-gray-200">AI Assistant</h3>
                  {(isGenerating || isAIResponding) && (
                    <Badge variant="outline" className="text-blue-400 border-blue-400 text-xs bg-blue-900/20">
                      Working...
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Generate NextJS components with Shadcn/UI
                </p>
              </div>

              {/* AI Chat Panel */}
              <div className="h-[calc(100%-80px)]">
                <DarkAiChatPanel />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Center Panel - Dynamic Properties */}
          <ResizablePanel defaultSize={35} minSize={30} maxSize={50}>
            <div className="h-full bg-gray-850 border-r border-gray-700">
              {/* Properties Header */}
              <div className="p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Settings className="w-4 h-4 text-green-400" />
                    <h3 className="font-medium text-gray-200">Dynamic Properties</h3>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Badge variant="secondary" className="text-xs bg-gray-700 text-gray-300">
                      {components.length} components
                    </Badge>
                    {selectedComponent && (
                      <Badge variant="outline" className="text-xs border-green-400 text-green-400 bg-green-900/20">
                        {selectedComponent.name}
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Configure component properties and behavior
                </p>
              </div>

              {/* Properties Content */}
              <div className="h-[calc(100%-80px)] bg-gray-900">
                <Tabs value={activeRightTab} onValueChange={setActiveRightTab} className="h-full">
                  <div className="border-b border-gray-700 bg-gray-800">
                    <TabsList className="w-full grid grid-cols-3 h-10 bg-transparent">
                      <TabsTrigger
                        value="properties"
                        className="flex items-center space-x-2 text-gray-400 data-[state=active]:text-white data-[state=active]:bg-gray-700"
                      >
                        <Settings className="w-3 h-3" />
                        <span className="text-xs">Properties</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="components"
                        className="flex items-center space-x-2 text-gray-400 data-[state=active]:text-white data-[state=active]:bg-gray-700"
                      >
                        <Layers className="w-3 h-3" />
                        <span className="text-xs">Components</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="nextjs"
                        className="flex items-center space-x-2 text-gray-400 data-[state=active]:text-white data-[state=active]:bg-gray-700"
                      >
                        <Globe className="w-3 h-3" />
                        <span className="text-xs">Next.js</span>
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="properties" className="h-[calc(100%-50px)] mt-0">
                    <DarkPropertiesPanel />
                  </TabsContent>

                  <TabsContent value="components" className="h-[calc(100%-50px)] mt-0">
                    <DarkComponentTree />
                  </TabsContent>

                  <TabsContent value="nextjs" className="h-[calc(100%-50px)] mt-0">
                    <div className="p-4 text-gray-300">
                      <div className="text-center">
                        <Globe className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                        <p className="text-sm">NextJS Layout Generator</p>
                        <p className="text-xs text-gray-500 mt-1">Coming soon...</p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Right Panel - Live Preview */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <div className="h-full bg-gray-900">
              {/* Preview Header */}
              <div className="p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Eye className="w-4 h-4 text-purple-400" />
                    <h3 className="font-medium text-gray-200">Live Preview</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                    >
                      <RotateCcw className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                    >
                      <Code className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Real-time component preview
                </p>
              </div>

              {/* Live Preview Content */}
              <div className="h-[calc(100%-80px)]">
                <LivePreview />
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  )
}
