'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Brain, Sparkles, Wand2, Zap, Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

export default function AIPageGenerator() {
  const router = useRouter()
  const [isGenerating, setIsGenerating] = useState(false)
  const [formData, setFormData] = useState({
    description: '',
    pageType: 'landing',
    targetAudience: '',
    goals: '',
    style: 'modern',
    industry: ''
  })

  const handleBack = () => {
    router.push('/admin/system/pages')
  }

  const handleGenerate = async () => {
    if (!formData.description.trim()) {
      toast.error('Please provide a page description')
      return
    }

    setIsGenerating(true)
    try {
      // Simulate AI generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Create a new page with AI-generated content
      const response = await fetch('/api/e-commerce/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `AI Generated: ${formData.pageType} Page`,
          slug: `ai-generated-${Date.now()}`,
          description: formData.description,
          type: formData.pageType,
          status: 'draft',
          aiGenerated: true,
          aiPrompt: formData.description,
          aiSettings: formData
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Page generated successfully!')
        router.push(`/admin/system/pages/${data.data.id}/ai-editor`)
      } else {
        throw new Error(data.error || 'Failed to generate page')
      }
    } catch (error) {
      console.error('Error generating page:', error)
      toast.error('Failed to generate page')
    } finally {
      setIsGenerating(false)
    }
  }

  const pageTypes = [
    { value: 'landing', label: 'Landing Page' },
    { value: 'product', label: 'Product Page' },
    { value: 'category', label: 'Category Page' },
    { value: 'about', label: 'About Page' },
    { value: 'contact', label: 'Contact Page' },
    { value: 'blog', label: 'Blog Page' },
    { value: 'custom', label: 'Custom Page' }
  ]

  const styles = [
    { value: 'modern', label: 'Modern & Clean' },
    { value: 'minimalist', label: 'Minimalist' },
    { value: 'bold', label: 'Bold & Vibrant' },
    { value: 'elegant', label: 'Elegant & Sophisticated' },
    { value: 'playful', label: 'Playful & Creative' },
    { value: 'professional', label: 'Professional & Corporate' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      {/* Header */}
      <div className="bg-white border-b border-purple-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Pages
            </Button>
            <div>
              <h1 className="text-2xl font-bold flex items-center space-x-2">
                <Brain className="h-6 w-6 text-purple-600" />
                <span>AI Page Generator</span>
              </h1>
              <p className="text-muted-foreground">
                Generate complete pages from natural language descriptions
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Wand2 className="h-5 w-5 text-purple-600" />
                    <span>Describe Your Page</span>
                  </CardTitle>
                  <CardDescription>
                    Tell our AI what kind of page you want to create. Be as detailed as possible for better results.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="description">Page Description *</Label>
                    <Textarea
                      id="description"
                      placeholder="Describe the page you want to create. For example: 'Create a landing page for a sustainable fashion brand targeting eco-conscious millennials. Include a hero section with environmental messaging, product showcase, customer testimonials, and a newsletter signup.'"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={4}
                      className="mt-2"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="pageType">Page Type</Label>
                      <Select value={formData.pageType} onValueChange={(value) => setFormData(prev => ({ ...prev, pageType: value }))}>
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {pageTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="style">Design Style</Label>
                      <Select value={formData.style} onValueChange={(value) => setFormData(prev => ({ ...prev, style: value }))}>
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {styles.map(style => (
                            <SelectItem key={style.value} value={style.value}>
                              {style.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="targetAudience">Target Audience</Label>
                    <Input
                      id="targetAudience"
                      placeholder="e.g., Young professionals, Parents, Tech enthusiasts"
                      value={formData.targetAudience}
                      onChange={(e) => setFormData(prev => ({ ...prev, targetAudience: e.target.value }))}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="goals">Page Goals</Label>
                    <Input
                      id="goals"
                      placeholder="e.g., Generate leads, Showcase products, Build brand awareness"
                      value={formData.goals}
                      onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="industry">Industry/Niche</Label>
                    <Input
                      id="industry"
                      placeholder="e.g., E-commerce, SaaS, Healthcare, Education"
                      value={formData.industry}
                      onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
                      className="mt-2"
                    />
                  </div>

                  <Button 
                    onClick={handleGenerate} 
                    disabled={isGenerating || !formData.description.trim()}
                    className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Generating Your Page...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-5 w-5 mr-2" />
                        Generate Page with AI
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">AI Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Zap className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Smart Content</h4>
                      <p className="text-sm text-muted-foreground">AI generates relevant content based on your description</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Brain className="h-5 w-5 text-purple-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Intelligent Layout</h4>
                      <p className="text-sm text-muted-foreground">Optimal section arrangement for your goals</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Sparkles className="h-5 w-5 text-pink-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Style Matching</h4>
                      <p className="text-sm text-muted-foreground">Design elements that match your brand</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tips for Better Results</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm text-muted-foreground">
                  <p>• Be specific about your page purpose</p>
                  <p>• Mention key sections you want</p>
                  <p>• Include your target audience</p>
                  <p>• Specify any special requirements</p>
                  <p>• Mention your brand personality</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
