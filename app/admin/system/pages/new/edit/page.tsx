"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { PageBuilderCanvas } from "@/lib/page-builder/components/page-builder-canvas";
import { FloatingAIChat } from "@/lib/page-builder/components/floating-ai-chat";
import { usePageBuilder } from "@/stores/use-page-builder";
import { PageData } from "@/lib/page-builder/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Save, Sparkles } from "lucide-react";
import { toast } from "sonner";
import { SidebarTrigger } from "@/components/ui/sidebar";

// Inner component that uses the PageBuilder context
function NewPageEditorContent() {
  const router = useRouter();
  const {
    page,
    devicePreview,
    isPreviewMode,
    hasUnsavedChanges,
    loadState,
    saveState,
  } = usePageBuilder();
  const [saving, setSaving] = useState(false);
  const [pageData, setPageData] = useState<PageData | null>(null);

  // Initialize with a new page
  useEffect(() => {
    const newPage = {
      id: "new",
      title: "New Page",
      slug: "new-page",
      description: "",
      status: "draft" as const,
      type: "custom" as const,
      blocks: [],
      settings: {
        title: "New Page",
      },
    };
    setPageData(newPage);
    loadState({ page: newPage });
  }, [loadState]);

  // Handle save
  const handleSave = async () => {
    if (!pageData) return;

    try {
      setSaving(true);

      const response = await fetch("/api/e-commerce/pages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: page.title,
          slug: page.slug,
          description: page.description,
          status: page.status,
          type: page.type,
          blocks: page.blocks,
          settings: page.settings,
        }),
      });

      const data = await response.json();

      if (data.success) {
        saveState(); // Mark as saved
        toast.success("Page created successfully!");
        // Redirect to the new page details
        router.push(`/admin/system/pages/${data.data.id}`);
      } else {
        throw new Error(data.error || "Failed to save page");
      }
    } catch (error) {
      console.error("Error saving page:", error);
      toast.error("Failed to save page");
    } finally {
      setSaving(false);
    }
  };

  // Handle back to page list
  const handleBack = () => {
    router.push("/admin/system/pages");
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header for new page */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <>
          <SidebarTrigger className="mr-2" />
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
          </>
          <div>
            <h1 className="text-lg font-semibold">Create New Page</h1>
            <p className="text-sm text-muted-foreground">
              Start building your new page with our visual editor
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/admin/system/pages/ai-visual-editor')}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Try AI Editor
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Page
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Editor for new page */}
      <div className="flex-1 overflow-hidden">
        <PageBuilderCanvas
          devicePreview={devicePreview || "desktop"}
          isPreviewMode={isPreviewMode || false}
        />
      </div>

      {/* Floating AI Chat */}
      <FloatingAIChat
        isOpen={false}
        onClose={() => {}}
      />
    </div>
  );
}

// Main component - PageBuilderProvider is provided by layout.tsx
export default function NewPageEditorPage() {
  return <NewPageEditorContent />;
}