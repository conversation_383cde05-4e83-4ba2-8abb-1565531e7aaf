'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Wand2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { NextJSLayoutGenerator } from '@/lib/ai-visual-editor/components/nextjs-layout-generator'

export default function AILayoutBuilder() {
  const router = useRouter()

  const handleBack = () => {
    router.push('/admin/system/pages')
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
          <div>
            <h1 className="text-lg font-semibold flex items-center space-x-2">
              <Wand2 className="h-5 w-5 text-green-600" />
              <span>AI Layout Builder</span>
            </h1>
            <p className="text-sm text-muted-foreground">
              Build responsive layouts with intelligent assistance
            </p>
          </div>
        </div>
      </div>

      {/* AI Layout Builder */}
      <div className="flex-1 overflow-hidden bg-white">
        <NextJSLayoutGenerator />
      </div>
    </div>
  )
}
