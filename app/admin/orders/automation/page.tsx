import { Metadata } from 'next'
import { OrderAutomationEngine } from '@/components/admin/orders'

export const metadata: Metadata = {
  title: 'Order Automation Engine | Admin',
  description: 'Intelligent automation system for order processing workflows',
}

// Mock orders data for automation
const mockOrders = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    status: 'pending' as const,
    financialStatus: 'pending' as const,
    fulfillmentStatus: 'unfulfilled' as const,
    customer: {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    items: [
      {
        id: '1',
        productId: 'prod-1',
        productTitle: 'Coconut Milk Powder',
        quantity: 2,
        unitPrice: { amount: 150, currency: 'ZAR' },
        totalPrice: { amount: 300, currency: 'ZAR' },
      }
    ],
    total: { amount: 350, currency: 'ZAR' },
    itemCount: 2,
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z'),
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    status: 'processing' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'partially_fulfilled' as const,
    customer: {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    items: [
      {
        id: '2',
        productId: 'prod-2',
        productTitle: 'Organic Coconut Oil',
        quantity: 1,
        unitPrice: { amount: 250, currency: 'ZAR' },
        totalPrice: { amount: 250, currency: 'ZAR' },
      }
    ],
    total: { amount: 299, currency: 'ZAR' },
    itemCount: 1,
    createdAt: new Date('2024-01-15T09:15:00Z'),
    updatedAt: new Date('2024-01-15T11:45:00Z'),
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    status: 'shipped' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'shipped' as const,
    customer: {
      id: '3',
      email: '<EMAIL>',
      firstName: 'Mike',
      lastName: 'Johnson',
    },
    items: [
      {
        id: '3',
        productId: 'prod-3',
        productTitle: 'Coconut Flour',
        quantity: 3,
        unitPrice: { amount: 120, currency: 'ZAR' },
        totalPrice: { amount: 360, currency: 'ZAR' },
      }
    ],
    total: { amount: 410, currency: 'ZAR' },
    itemCount: 3,
    createdAt: new Date('2024-01-14T14:20:00Z'),
    updatedAt: new Date('2024-01-15T08:30:00Z'),
  },
  {
    id: '4',
    orderNumber: 'ORD-004',
    status: 'confirmed' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'unfulfilled' as const,
    customer: {
      id: '4',
      email: '<EMAIL>',
      firstName: 'Sarah',
      lastName: 'Wilson',
    },
    items: [
      {
        id: '4',
        productId: 'prod-1',
        productTitle: 'Coconut Milk Powder',
        quantity: 5,
        unitPrice: { amount: 150, currency: 'ZAR' },
        totalPrice: { amount: 750, currency: 'ZAR' },
      },
      {
        id: '5',
        productId: 'prod-2',
        productTitle: 'Organic Coconut Oil',
        quantity: 2,
        unitPrice: { amount: 250, currency: 'ZAR' },
        totalPrice: { amount: 500, currency: 'ZAR' },
      }
    ],
    total: { amount: 1350, currency: 'ZAR' },
    itemCount: 7,
    createdAt: new Date('2024-01-15T12:00:00Z'),
    updatedAt: new Date('2024-01-15T12:00:00Z'),
  },
]

export default function OrderAutomationPage() {
  const handleRuleUpdate = (rule: any) => {
    // Implement rule update logic
    console.log('Automation rule updated:', rule.name)
    // Save rule to database
    // Update automation engine
  }

  const handleRuleDelete = (ruleId: string) => {
    // Implement rule deletion logic
    console.log('Automation rule deleted:', ruleId)
    // Remove rule from database
    // Update automation engine
  }

  return (
    <OrderAutomationEngine
      orders={mockOrders}
      onRuleUpdate={handleRuleUpdate}
      onRuleDelete={handleRuleDelete}
    />
  )
}
