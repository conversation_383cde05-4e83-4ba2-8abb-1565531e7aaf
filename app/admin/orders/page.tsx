'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { EnhancedOrderList } from '@/components/admin/orders/enhanced-order-list'
import { Order } from '@/lib/ecommerce/types'
import { Activity, Package, Warehouse, Zap, Plus } from 'lucide-react'

export default function OrdersPage() {
  const router = useRouter()
  
  const { orders, loading, pagination, searchOrders } = useOrders({
    initialParams: {
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },
    autoFetch: true
  })

  const handleCreateOrder = useCallback(() => {
    router.push('/admin/orders/new')
  }, [router])

  const handleEditOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}/edit`)
  }, [router])

  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}`)
  }, [router])

  // Quick access handlers
  const handleProcessingDashboard = useCallback(() => {
    router.push('/admin/orders/processing')
  }, [router])

  const handleFulfillmentCenter = useCallback(() => {
    router.push('/admin/orders/fulfillment')
  }, [router])

  const handleInventoryManager = useCallback(() => {
    router.push('/admin/orders/inventory')
  }, [router])

  const handleAutomationEngine = useCallback(() => {
    router.push('/admin/orders/automation')
  }, [router])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Orders</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Quick Access to Processing Tools */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleProcessingDashboard}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Dashboard</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Monitor</div>
            <p className="text-xs text-muted-foreground">
              Order processing workflows
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleFulfillmentCenter}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fulfillment Center</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Process</div>
            <p className="text-xs text-muted-foreground">
              Order fulfillments
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleInventoryManager}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Manager</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Manage</div>
            <p className="text-xs text-muted-foreground">
              Stock levels & reservations
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleAutomationEngine}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Automation Engine</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Automate</div>
            <p className="text-xs text-muted-foreground">
              Processing rules
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Order List */}
      <EnhancedOrderList
        onCreateOrder={handleCreateOrder}
        onEditOrder={handleEditOrder}
        onViewOrder={handleViewOrder}
      />
    </div>
  )
}
