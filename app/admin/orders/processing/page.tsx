import { Metadata } from 'next'
import { OrderProcessingDashboard } from '@/components/admin/orders'

export const metadata: Metadata = {
  title: 'Order Processing Dashboard | Admin',
  description: 'Monitor and manage order processing workflows',
}

// Mock orders data - replace with actual data fetching
const mockOrders = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    status: 'pending' as const,
    financialStatus: 'pending' as const,
    fulfillmentStatus: 'unfulfilled' as const,
    customer: {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    items: [
      {
        id: '1',
        productId: 'prod-1',
        productTitle: 'Coconut Milk Powder',
        quantity: 2,
        unitPrice: { amount: 150, currency: 'ZAR' },
        totalPrice: { amount: 300, currency: 'ZAR' },
      }
    ],
    total: { amount: 350, currency: 'ZAR' },
    itemCount: 2,
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z'),
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    status: 'processing' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'partially_fulfilled' as const,
    customer: {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    items: [
      {
        id: '2',
        productId: 'prod-2',
        productTitle: 'Organic Coconut Oil',
        quantity: 1,
        unitPrice: { amount: 250, currency: 'ZAR' },
        totalPrice: { amount: 250, currency: 'ZAR' },
      }
    ],
    total: { amount: 299, currency: 'ZAR' },
    itemCount: 1,
    createdAt: new Date('2024-01-15T09:15:00Z'),
    updatedAt: new Date('2024-01-15T11:45:00Z'),
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    status: 'shipped' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'shipped' as const,
    customer: {
      id: '3',
      email: '<EMAIL>',
      firstName: 'Mike',
      lastName: 'Johnson',
    },
    items: [
      {
        id: '3',
        productId: 'prod-3',
        productTitle: 'Coconut Flour',
        quantity: 3,
        unitPrice: { amount: 120, currency: 'ZAR' },
        totalPrice: { amount: 360, currency: 'ZAR' },
      }
    ],
    total: { amount: 410, currency: 'ZAR' },
    itemCount: 3,
    createdAt: new Date('2024-01-14T14:20:00Z'),
    updatedAt: new Date('2024-01-15T08:30:00Z'),
  },
]

export default function OrderProcessingPage() {
  const handleRefresh = () => {
    // Implement refresh logic
    console.log('Refreshing order data...')
  }

  const handleProcessOrder = (order: any) => {
    // Implement order processing logic
    console.log('Processing order:', order.orderNumber)
  }

  const handleBulkProcess = (orders: any[]) => {
    // Implement bulk processing logic
    console.log('Bulk processing orders:', orders.map(o => o.orderNumber))
  }

  return (
    <OrderProcessingDashboard
      orders={mockOrders}
      onRefresh={handleRefresh}
      onProcessOrder={handleProcessOrder}
      onBulkProcess={handleBulkProcess}
    />
  )
}
