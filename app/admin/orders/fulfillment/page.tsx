import { Metadata } from 'next'
import { OrderFulfillmentCenter } from '@/components/admin/orders'

export const metadata: Metadata = {
  title: 'Order Fulfillment Center | Admin',
  description: 'Process order fulfillments with barcode scanning and multi-location support',
}

// Mock order data for fulfillment - replace with actual data fetching
const mockOrder = {
  id: '1',
  orderNumber: 'ORD-001',
  status: 'confirmed' as const,
  financialStatus: 'paid' as const,
  fulfillmentStatus: 'unfulfilled' as const,
  customer: {
    id: '1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Doe',
    phone: '+27 82 123 4567',
  },
  shippingAddress: {
    firstName: 'John',
    lastName: 'Doe',
    company: 'Acme Corp',
    address1: '123 Main Street',
    address2: 'Suite 456',
    city: 'Cape Town',
    province: 'Western Cape',
    postalCode: '8001',
    country: 'South Africa',
    phone: '+27 82 123 4567',
  },
  billingAddress: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    company: 'Acme Corp',
    address1: '123 Main Street',
    address2: 'Suite 456',
    city: 'Cape Town',
    province: 'Western Cape',
    postalCode: '8001',
    country: 'South Africa',
    phone: '+27 82 123 4567',
  },
  items: [
    {
      id: '1',
      productId: 'prod-1',
      variantId: 'var-1',
      productTitle: 'Coconut Milk Powder',
      variantTitle: '500g',
      sku: 'COCO-001-500G',
      quantity: 2,
      unitPrice: { amount: 150, currency: 'ZAR' },
      totalPrice: { amount: 300, currency: 'ZAR' },
    },
    {
      id: '2',
      productId: 'prod-2',
      variantId: 'var-2',
      productTitle: 'Organic Coconut Oil',
      variantTitle: '250ml',
      sku: 'COCO-002-250ML',
      quantity: 1,
      unitPrice: { amount: 250, currency: 'ZAR' },
      totalPrice: { amount: 250, currency: 'ZAR' },
    },
  ],
  shippingMethod: {
    id: 'standard',
    title: 'Standard Shipping',
    price: { amount: 50, currency: 'ZAR' },
  },
  paymentMethod: {
    id: 'card',
    title: 'Credit Card',
    last4: '4242',
  },
  subtotal: { amount: 550, currency: 'ZAR' },
  shippingCost: { amount: 50, currency: 'ZAR' },
  tax: { amount: 0, currency: 'ZAR' },
  total: { amount: 600, currency: 'ZAR' },
  itemCount: 3,
  createdAt: new Date('2024-01-15T10:30:00Z'),
  updatedAt: new Date('2024-01-15T10:30:00Z'),
  notes: 'Customer requested expedited processing',
}

export default function OrderFulfillmentPage() {
  const handleFulfillmentComplete = (fulfillmentData: any) => {
    // Implement fulfillment completion logic
    console.log('Fulfillment completed:', fulfillmentData)
    // Redirect back to orders list or show success message
  }

  const handleClose = () => {
    // Implement close logic - redirect back to orders
    console.log('Closing fulfillment center')
    // window.history.back() or router.push('/admin/orders')
  }

  return (
    <OrderFulfillmentCenter
      order={mockOrder}
      onFulfillmentComplete={handleFulfillmentComplete}
      onClose={handleClose}
    />
  )
}
