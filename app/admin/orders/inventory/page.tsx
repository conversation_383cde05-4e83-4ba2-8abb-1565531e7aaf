import { Metadata } from 'next'
import { OrderInventoryManager } from '@/components/admin/orders'

export const metadata: Metadata = {
  title: 'Order Inventory Manager | Admin',
  description: 'Manage inventory levels and reservations for order processing',
}

// Mock orders data for inventory management
const mockOrders = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    status: 'pending' as const,
    financialStatus: 'pending' as const,
    fulfillmentStatus: 'unfulfilled' as const,
    customer: {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    items: [
      {
        id: '1',
        productId: 'prod-1',
        variantId: 'var-1',
        productTitle: 'Coconut Milk Powder',
        variantTitle: '500g',
        sku: 'COCO-001-500G',
        quantity: 2,
        unitPrice: { amount: 150, currency: 'ZAR' },
        totalPrice: { amount: 300, currency: 'ZAR' },
      }
    ],
    total: { amount: 350, currency: 'ZAR' },
    itemCount: 2,
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-15T10:30:00Z'),
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    status: 'confirmed' as const,
    financialStatus: 'paid' as const,
    fulfillmentStatus: 'unfulfilled' as const,
    customer: {
      id: '2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    items: [
      {
        id: '2',
        productId: 'prod-2',
        variantId: 'var-2',
        productTitle: 'Organic Coconut Oil',
        variantTitle: '250ml',
        sku: 'COCO-002-250ML',
        quantity: 1,
        unitPrice: { amount: 250, currency: 'ZAR' },
        totalPrice: { amount: 250, currency: 'ZAR' },
      },
      {
        id: '3',
        productId: 'prod-3',
        variantId: 'var-3',
        productTitle: 'Coconut Flour',
        variantTitle: '1kg',
        sku: 'COCO-003-1KG',
        quantity: 1,
        unitPrice: { amount: 180, currency: 'ZAR' },
        totalPrice: { amount: 180, currency: 'ZAR' },
      }
    ],
    total: { amount: 479, currency: 'ZAR' },
    itemCount: 2,
    createdAt: new Date('2024-01-15T09:15:00Z'),
    updatedAt: new Date('2024-01-15T11:45:00Z'),
  },
]

export default function OrderInventoryPage() {
  const handleInventoryUpdate = (item: any) => {
    // Implement inventory update logic
    console.log('Inventory updated for item:', item.sku)
    // Update inventory in database
    // Refresh inventory data
  }

  const handleReservationUpdate = (orderId: string, items: any[]) => {
    // Implement reservation update logic
    console.log('Reservations updated for order:', orderId, items)
    // Update reservations in database
    // Send notifications if needed
  }

  return (
    <OrderInventoryManager
      orders={mockOrders}
      onInventoryUpdate={handleInventoryUpdate}
      onReservationUpdate={handleReservationUpdate}
    />
  )
}
