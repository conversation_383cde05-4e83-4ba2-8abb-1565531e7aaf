@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base font is handled by <PERSON><PERSON><PERSON> through the sans font family */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
  --background: 223.8136 0.0005% 98.6829%;
  --foreground: 0 0% 0%;
  --card: 223.8136 -172.5242% 100.0000%;
  --card-foreground: 0 0% 0%;
  --popover: 223.8136 0.0005% 98.6829%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 223.8136 -172.5242% 100.0000%;
  --secondary: 223.8136 0.0001% 92.1478%;
  --secondary-foreground: 0 0% 0%;
  --muted: 223.8136 0.0002% 96.0587%;
  --muted-foreground: 223.8136 0.0000% 32.3067%;
  --accent: 223.8136 0.0001% 92.1478%;
  --accent-foreground: 0 0% 0%;
  --destructive: 358.4334 74.9120% 59.7455%;
  --destructive-foreground: 223.8136 -172.5242% 100.0000%;
  --border: 223.8136 0.0001% 89.5577%;
  --input: 223.8136 0.0001% 92.1478%;
  --ring: 0 0% 0%;
  --chart-1: 40.6655 100.2361% 50.9228%;
  --chart-2: 223.7490 85.9924% 55.8092%;
  --chart-3: 223.8136 0.0000% 64.4710%;
  --chart-4: 223.8136 0.0001% 89.5577%;
  --chart-5: 223.8136 0.0000% 45.6078%;
  --sidebar: 223.8136 0.0005% 98.6829%;
  --sidebar-foreground: 0 0% 0%;
  --sidebar-primary: 0 0% 0%;
  --sidebar-primary-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-accent: 223.8136 0.0001% 92.1478%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 223.8136 0.0001% 92.1478%;
  --sidebar-ring: 0 0% 0%;
  --font-sans: Outfit, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow-md: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 2px 4px -2px hsl(0 0% 0% / 0.18);
  --shadow-lg: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 4px 6px -2px hsl(0 0% 0% / 0.18);
  --shadow-xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 8px 10px -2px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.45);
  --spacing: 0.2rem;

  /* AI Visual Builder Color Schemes */
  --builder-primary: 59 130 246; /* Blue for Visual Editor */
  --builder-secondary: 34 197 94; /* Green for NextJS Generator */
  --builder-accent: 99 102 241; /* Indigo for Page Builder */
  --builder-warning: 245 158 11; /* Amber for Theme Generator */

  /* Builder-specific backgrounds */
  --builder-bg-primary: 239 246 255; /* Blue 50 */
  --builder-bg-secondary: 240 253 244; /* Green 50 */
  --builder-bg-accent: 238 242 255; /* Indigo 50 */
  --builder-bg-warning: 255 251 235; /* Amber 50 */

  /* Drag and Drop Zone Variables */
  --drop-zone-border: 2px dashed hsl(var(--builder-primary) / 0.3);
  --drop-zone-bg: hsl(var(--builder-primary) / 0.05);
  --drop-zone-hover-border: 2px dashed hsl(var(--builder-primary) / 0.6);
  --drop-zone-hover-bg: hsl(var(--builder-primary) / 0.1);
  --drop-zone-active-border: 2px solid hsl(var(--builder-primary));
  --drop-zone-active-bg: hsl(var(--builder-primary) / 0.15);

  /* Animation Variables */
  --builder-transition-fast: 150ms ease-out;
  --builder-transition-normal: 250ms ease-out;
  --builder-transition-slow: 350ms ease-out;
  --builder-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --builder-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Builder Typography Scale */
  --builder-text-xs: 0.75rem;
  --builder-text-sm: 0.875rem;
  --builder-text-base: 1rem;
  --builder-text-lg: 1.125rem;
  --builder-text-xl: 1.25rem;
  --builder-line-height-tight: 1.25;
  --builder-line-height-normal: 1.5;
  --builder-line-height-relaxed: 1.75;

  /* Builder Spacing Scale */
  --builder-space-1: 0.25rem;
  --builder-space-2: 0.5rem;
  --builder-space-3: 0.75rem;
  --builder-space-4: 1rem;
  --builder-space-6: 1.5rem;
  --builder-space-8: 2rem;
  --builder-space-12: 3rem;

  /* Builder Panel Dimensions */
  --builder-panel-width-sm: 280px;
  --builder-panel-width-md: 320px;
  --builder-panel-width-lg: 380px;
  --builder-toolbar-height: 48px;
  --builder-header-height: 56px;
}

.dark {
  --background: 0 0% 0%;
  --foreground: 223.8136 -172.5242% 100.0000%;
  --card: 223.8136 0.0000% 3.5452%;
  --card-foreground: 223.8136 -172.5242% 100.0000%;
  --popover: 223.8136 0.0000% 6.8692%;
  --popover-foreground: 223.8136 -172.5242% 100.0000%;
  --primary: 223.8136 -172.5242% 100.0000%;
  --primary-foreground: 0 0% 0%;
  --secondary: 223.8136 0.0000% 13.1499%;
  --secondary-foreground: 223.8136 -172.5242% 100.0000%;
  --muted: 223.8136 0.0000% 11.3040%;
  --muted-foreground: 223.8136 0.0000% 64.4710%;
  --accent: 223.8136 0.0000% 19.8916%;
  --accent-foreground: 223.8136 -172.5242% 100.0000%;
  --destructive: 359.9132 100.2494% 67.8807%;
  --destructive-foreground: 0 0% 0%;
  --border: 223.8136 0.0000% 14.0871%;
  --input: 223.8136 0.0000% 19.8916%;
  --ring: 223.8136 0.0000% 64.4710%;
  --chart-1: 40.6655 100.2361% 50.9228%;
  --chart-2: 218.1624 90.0354% 55.1618%;
  --chart-3: 223.8136 0.0000% 45.6078%;
  --chart-4: 223.8136 0.0000% 32.3067%;
  --chart-5: 223.8136 0.0001% 89.5577%;
  --sidebar: 223.8136 0.0000% 6.8692%;
  --sidebar-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-primary: 223.8136 -172.5242% 100.0000%;
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: 223.8136 0.0000% 19.8916%;
  --sidebar-accent-foreground: 223.8136 -172.5242% 100.0000%;
  --sidebar-border: 223.8136 0.0000% 19.8916%;
  --sidebar-ring: 223.8136 0.0000% 64.4710%;
  --font-sans: Outfit, sans-serif;
  --font-serif: Plus Jakarta Sans, sans-serif;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-xs: 0px 1px 2px -1px hsl(0 0% 0% / 0.09);
  --shadow-sm: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 1px 2px -2px hsl(0 0% 0% / 0.18);
  --shadow-md: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 2px 4px -2px hsl(0 0% 0% / 0.18);
  --shadow-lg: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 4px 6px -2px hsl(0 0% 0% / 0.18);
  --shadow-xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.18), 0px 8px 10px -2px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 0px 1px 2px -1px hsl(0 0% 0% / 0.45);

  /* AI Visual Builder Dark Mode Colors */
  --builder-primary: 96 165 250; /* Blue 400 for dark mode */
  --builder-secondary: 74 222 128; /* Green 400 for dark mode */
  --builder-accent: 129 140 248; /* Indigo 400 for dark mode */
  --builder-warning: 251 191 36; /* Amber 400 for dark mode */

  /* Builder-specific dark backgrounds */
  --builder-bg-primary: 30 58 138; /* Blue 900 */
  --builder-bg-secondary: 20 83 45; /* Green 900 */
  --builder-bg-accent: 49 46 129; /* Indigo 900 */
  --builder-bg-warning: 146 64 14; /* Amber 900 */

  /* Dark mode drop zones */
  --drop-zone-border: 2px dashed hsl(var(--builder-primary) / 0.4);
  --drop-zone-bg: hsl(var(--builder-primary) / 0.08);
  --drop-zone-hover-border: 2px dashed hsl(var(--builder-primary) / 0.7);
  --drop-zone-hover-bg: hsl(var(--builder-primary) / 0.15);
  --drop-zone-active-border: 2px solid hsl(var(--builder-primary));
  --drop-zone-active-bg: hsl(var(--builder-primary) / 0.2);
}
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Clean Typography - Selfi.co.za inspired */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.2;
}

body {
  letter-spacing: 0.005em;
  font-weight: 400;
  line-height: 1.5;
}

/* Clean product titles */
.product-title {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.3;
  letter-spacing: 0.01em;
}

/* Clean Animations - Selfi.co.za inspired */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Subtle hover effects */
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.image-zoom {
  overflow: hidden;
}

.image-zoom img {
  transition: transform 0.6s ease;
}

.image-zoom:hover img {
  transform: scale(1.02);
}

/* Clean Button Styles - Selfi.co.za inspired */
.btn-minimal {
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.2s ease;
  position: relative;
}

.btn-minimal::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.btn-minimal:hover::after {
  width: 100%;
}

/* Clean Product Card */
.product-card-clean {
  transition: opacity 0.2s ease;
}

.product-card-clean:hover {
  opacity: 0.8;
}

/* Simple Hover Effects */
.hover-underline {
  position: relative;
}

.hover-underline::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.2s ease;
}

.hover-underline:hover::after {
  width: 100%;
}

/* Clean borders */
.border-clean {
  border: 1px solid #f0f0f0;
}

/* Minimal spacing */
.space-clean > * + * {
  margin-top: 0.5rem;
}

/* ===== AI VISUAL BUILDER STYLES ===== */

/* Builder Theme Classes */
.builder-theme-visual {
  --current-builder-primary: var(--builder-primary);
  --current-builder-bg: var(--builder-bg-primary);
}

.builder-theme-nextjs {
  --current-builder-primary: var(--builder-secondary);
  --current-builder-bg: var(--builder-bg-secondary);
}

.builder-theme-page {
  --current-builder-primary: var(--builder-accent);
  --current-builder-bg: var(--builder-bg-accent);
}

.builder-theme-theme {
  --current-builder-primary: var(--builder-warning);
  --current-builder-bg: var(--builder-bg-warning);
}

/* Drag and Drop Zones */
.drop-zone {
  border: var(--drop-zone-border);
  background: var(--drop-zone-bg);
  transition: all var(--builder-transition-normal);
  border-radius: 8px;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover {
  border: var(--drop-zone-hover-border);
  background: var(--drop-zone-hover-bg);
  transform: scale(1.01);
}

.drop-zone.drag-over {
  border: var(--drop-zone-active-border);
  background: var(--drop-zone-active-bg);
  transform: scale(1.02);
  box-shadow: 0 8px 25px -8px hsl(var(--current-builder-primary) / 0.3);
}

.drop-zone-indicator {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    hsl(var(--current-builder-primary) / 0.1),
    hsl(var(--current-builder-primary) / 0.05)
  );
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--builder-transition-normal);
}

.drop-zone.drag-over .drop-zone-indicator {
  opacity: 1;
}

/* Builder Animations */
@keyframes builderSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes builderSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes builderPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes builderShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.builder-slide-in {
  animation: builderSlideIn var(--builder-transition-normal) var(--builder-smooth);
}

.builder-slide-up {
  animation: builderSlideUp var(--builder-transition-normal) var(--builder-smooth);
}

.builder-pulse {
  animation: builderPulse 2s infinite;
}

.builder-shake {
  animation: builderShake 0.5s ease-in-out;
}

/* Builder Panel Styles */
.builder-panel {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  box-shadow: 0 4px 12px -4px hsl(var(--foreground) / 0.1);
  transition: all var(--builder-transition-normal);
}

.builder-panel:hover {
  box-shadow: 0 8px 25px -8px hsl(var(--foreground) / 0.15);
  transform: translateY(-2px);
}

.builder-panel-header {
  padding: var(--builder-space-4);
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.3);
  border-radius: 12px 12px 0 0;
}

.builder-panel-content {
  padding: var(--builder-space-4);
}

/* Properties Panel Enhancements */
.properties-section {
  border-bottom: 1px solid hsl(var(--border));
  transition: all var(--builder-transition-fast);
}

.properties-section:last-child {
  border-bottom: none;
}

.properties-section-header {
  padding: var(--builder-space-3) var(--builder-space-4);
  background: hsl(var(--muted) / 0.2);
  border-bottom: 1px solid hsl(var(--border));
  display: flex;
  align-items: center;
  gap: var(--builder-space-2);
  font-size: var(--builder-text-sm);
  font-weight: 500;
  color: hsl(var(--foreground) / 0.8);
}

.properties-section-content {
  padding: var(--builder-space-4);
}

/* Field Groups */
.field-group {
  margin-bottom: var(--builder-space-6);
}

.field-group:last-child {
  margin-bottom: 0;
}

.field-label {
  display: block;
  font-size: var(--builder-text-sm);
  font-weight: 500;
  color: hsl(var(--foreground) / 0.9);
  margin-bottom: var(--builder-space-2);
}

.field-description {
  font-size: var(--builder-text-xs);
  color: hsl(var(--muted-foreground));
  margin-top: var(--builder-space-1);
  line-height: var(--builder-line-height-normal);
}

/* Interactive Elements */
.builder-button {
  display: inline-flex;
  align-items: center;
  gap: var(--builder-space-2);
  padding: var(--builder-space-2) var(--builder-space-4);
  font-size: var(--builder-text-sm);
  font-weight: 500;
  border-radius: 6px;
  transition: all var(--builder-transition-fast);
  cursor: pointer;
  border: 1px solid transparent;
}

.builder-button-primary {
  background: hsl(var(--current-builder-primary));
  color: white;
  border-color: hsl(var(--current-builder-primary));
}

.builder-button-primary:hover {
  background: hsl(var(--current-builder-primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -4px hsl(var(--current-builder-primary) / 0.4);
}

.builder-button-secondary {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border-color: hsl(var(--border));
}

.builder-button-secondary:hover {
  background: hsl(var(--secondary) / 0.8);
  border-color: hsl(var(--current-builder-primary) / 0.3);
}

.builder-button-ghost {
  background: transparent;
  color: hsl(var(--foreground) / 0.7);
}

.builder-button-ghost:hover {
  background: hsl(var(--current-builder-primary) / 0.1);
  color: hsl(var(--current-builder-primary));
}

/* Loading States */
.builder-loading {
  position: relative;
  overflow: hidden;
}

.builder-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    hsl(var(--current-builder-primary) / 0.2),
    transparent
  );
  animation: builderShimmer 1.5s infinite;
}

@keyframes builderShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Component Tree Styles */
.component-tree-item {
  padding: var(--builder-space-2) var(--builder-space-3);
  border-radius: 6px;
  transition: all var(--builder-transition-fast);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--builder-space-2);
}

.component-tree-item:hover {
  background: hsl(var(--current-builder-primary) / 0.1);
}

.component-tree-item.selected {
  background: hsl(var(--current-builder-primary) / 0.15);
  border: 1px solid hsl(var(--current-builder-primary) / 0.3);
}

.component-tree-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Responsive Builder Layouts */
@media (max-width: 768px) {
  .builder-panel {
    border-radius: 8px;
  }

  .builder-panel-header,
  .builder-panel-content {
    padding: var(--builder-space-3);
  }

  .properties-section-header {
    padding: var(--builder-space-2) var(--builder-space-3);
  }

  .properties-section-content {
    padding: var(--builder-space-3);
  }

  .drop-zone {
    min-height: 80px;
  }

  .builder-button {
    padding: var(--builder-space-2) var(--builder-space-3);
    font-size: var(--builder-text-xs);
  }
}

@media (max-width: 480px) {
  .builder-panel-header,
  .builder-panel-content {
    padding: var(--builder-space-2);
  }

  .drop-zone {
    min-height: 60px;
  }
}

/* Accessibility Enhancements */
.builder-focus-visible:focus-visible {
  outline: 2px solid hsl(var(--current-builder-primary));
  outline-offset: 2px;
  border-radius: 4px;
}

.builder-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .drop-zone {
    border-width: 3px;
  }

  .builder-button {
    border-width: 2px;
  }

  .component-tree-item.selected {
    border-width: 2px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .drop-zone,
  .builder-panel,
  .builder-button,
  .component-tree-item {
    transition: none;
  }

  .builder-slide-in,
  .builder-slide-up,
  .builder-pulse,
  .builder-shake {
    animation: none;
  }

  .builder-loading::after {
    animation: none;
  }
}

/* Tooltips and Help Indicators */
.builder-tooltip {
  position: relative;
  display: inline-block;
}

.builder-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  padding: var(--builder-space-2) var(--builder-space-3);
  border-radius: 6px;
  font-size: var(--builder-text-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--builder-transition-fast);
  z-index: 1000;
  border: 1px solid hsl(var(--border));
  box-shadow: 0 4px 12px -4px hsl(var(--foreground) / 0.1);
}

.builder-tooltip:hover::after {
  opacity: 1;
}

.builder-help-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  font-size: 10px;
  font-weight: 600;
  cursor: help;
  transition: all var(--builder-transition-fast);
}

.builder-help-indicator:hover {
  background: hsl(var(--current-builder-primary));
  color: white;
  transform: scale(1.1);
}

/* Status Indicators */
.builder-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--builder-space-1);
  padding: var(--builder-space-1) var(--builder-space-2);
  border-radius: 12px;
  font-size: var(--builder-text-xs);
  font-weight: 500;
}

.builder-status-ready {
  background: hsl(var(--builder-secondary) / 0.1);
  color: hsl(var(--builder-secondary));
}

.builder-status-beta {
  background: hsl(var(--builder-accent) / 0.1);
  color: hsl(var(--builder-accent));
}

.builder-status-coming-soon {
  background: hsl(var(--builder-warning) / 0.1);
  color: hsl(var(--builder-warning));
}
