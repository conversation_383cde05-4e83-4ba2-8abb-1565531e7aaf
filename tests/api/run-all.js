#!/usr/bin/env node

/**
 * Test Runner - Runs all API tests in sequence
 * This script executes all test files and provides a summary
 */

const { spawn } = require('child_process')
const path = require('path')

const tests = [
  {
    name: 'API Health Check',
    file: 'health-check.js',
    description: 'Basic API and database connectivity'
  },
  {
    name: 'Product CRUD',
    file: 'product-crud.js',
    description: 'Complete product CRUD operations'
  },
  {
    name: 'Product Update Scenarios',
    file: 'product-update-scenarios.js',
    description: 'Various product update scenarios'
  },
  {
    name: 'Order Health Check',
    file: 'order-health-check.js',
    description: 'Order API functionality check'
  },
  {
    name: 'Order CRUD',
    file: 'order-crud.js',
    description: 'Complete order CRUD operations'
  },
  {
    name: 'Order Update Scenarios',
    file: 'order-update-scenarios.js',
    description: 'Various order update scenarios'
  }
]

async function runTest(testFile, testName) {
  return new Promise((resolve) => {
    console.log(`\n${'='.repeat(80)}`)
    console.log(`🧪 Running: ${testName}`)
    console.log(`📁 File: ${testFile}`)
    console.log(`${'='.repeat(80)}`)

    const testPath = path.join(__dirname, testFile)
    const child = spawn('node', [testPath], {
      stdio: 'inherit',
      cwd: process.cwd()
    })

    const startTime = Date.now()

    child.on('close', (code) => {
      const endTime = Date.now()
      const duration = ((endTime - startTime) / 1000).toFixed(2)
      
      console.log(`\n${'─'.repeat(80)}`)
      console.log(`📊 Test "${testName}" completed in ${duration}s`)
      console.log(`🔢 Exit code: ${code}`)
      console.log(`${code === 0 ? '✅ PASSED' : '❌ FAILED'}`)
      console.log(`${'─'.repeat(80)}`)

      resolve({
        name: testName,
        file: testFile,
        success: code === 0,
        duration: parseFloat(duration),
        exitCode: code
      })
    })

    child.on('error', (error) => {
      console.error(`❌ Failed to run test ${testName}:`, error.message)
      resolve({
        name: testName,
        file: testFile,
        success: false,
        duration: 0,
        error: error.message
      })
    })
  })
}

async function runAllTests() {
  console.log('🚀 Starting API Test Suite')
  console.log(`📅 ${new Date().toISOString()}`)
  console.log(`🔧 Node.js ${process.version}`)
  console.log(`📂 Working Directory: ${process.cwd()}`)

  const results = []
  const startTime = Date.now()

  // Check if server is likely running
  try {
    const response = await fetch('http://localhost:3090')
    console.log('✅ Server appears to be running')
  } catch (error) {
    console.log('⚠️  Warning: Server may not be running on localhost:3090')
    console.log('💡 Make sure to run "npm run dev" before running tests')
  }

  // Run each test
  for (const test of tests) {
    const result = await runTest(test.file, test.name)
    result.description = test.description
    results.push(result)

    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  const endTime = Date.now()
  const totalDuration = ((endTime - startTime) / 1000).toFixed(2)

  // Generate summary
  console.log(`\n${'='.repeat(80)}`)
  console.log('📋 TEST SUITE SUMMARY')
  console.log(`${'='.repeat(80)}`)

  const passed = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length
  const total = results.length

  console.log(`📊 Results: ${passed}/${total} tests passed`)
  console.log(`⏱️  Total Duration: ${totalDuration}s`)
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`)

  console.log(`\n📝 Detailed Results:`)
  results.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL'
    const duration = result.duration ? `${result.duration}s` : 'N/A'
    console.log(`  ${index + 1}. ${status} ${result.name} (${duration})`)
    console.log(`     ${result.description}`)
    if (result.error) {
      console.log(`     Error: ${result.error}`)
    }
  })

  if (failed > 0) {
    console.log(`\n🔧 Troubleshooting:`)
    console.log(`   1. Check that the development server is running: npm run dev`)
    console.log(`   2. Verify database connectivity`)
    console.log(`   3. Check server logs for detailed error messages`)
    console.log(`   4. Run individual tests for more specific debugging`)
  }

  console.log(`\n${'='.repeat(80)}`)
  console.log(`🏁 Test Suite ${passed === total ? 'COMPLETED SUCCESSFULLY' : 'COMPLETED WITH FAILURES'}`)
  console.log(`${'='.repeat(80)}`)

  // Exit with appropriate code
  process.exit(failed > 0 ? 1 : 0)
}

// Handle process interruption
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Test suite interrupted by user')
  process.exit(130)
})

process.on('SIGTERM', () => {
  console.log('\n\n⚠️  Test suite terminated')
  process.exit(143)
})

// Run all tests
runAllTests().catch((error) => {
  console.error('❌ Test suite failed with error:', error)
  process.exit(1)
})
