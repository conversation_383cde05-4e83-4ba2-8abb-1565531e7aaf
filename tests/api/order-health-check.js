#!/usr/bin/env node

/**
 * Order API Health Check Script
 * Tests basic connectivity and order API functionality
 */

const BASE_URL = 'http://localhost:3090'

async function checkEndpoint(url, description, options = {}) {
  try {
    console.log(`\n🔍 Checking ${description}...`)
    console.log(`   URL: ${url}`)
    
    const startTime = Date.now()
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    const endTime = Date.now()
    
    const responseTime = endTime - startTime
    console.log(`   Status: ${response.status}`)
    console.log(`   Response Time: ${responseTime}ms`)
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      try {
        const data = await response.json()
        console.log(`   Response: ${JSON.stringify(data, null, 2)}`)
        return { success: response.ok, data, status: response.status, responseTime }
      } catch (e) {
        console.log(`   Response: [<PERSON>SO<PERSON> Parse Error]`)
        return { success: false, error: 'Invalid JSON', status: response.status, responseTime }
      }
    } else {
      const text = await response.text()
      console.log(`   Response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}`)
      return { success: response.ok, data: text, status: response.status, responseTime }
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function testOrderEndpoints() {
  console.log('\n📦 Testing Order API Endpoints...')
  
  const endpoints = [
    {
      url: `${BASE_URL}/api/e-commerce/orders`,
      description: 'GET Orders List',
      method: 'GET'
    },
    {
      url: `${BASE_URL}/api/e-commerce/orders?limit=1`,
      description: 'GET Orders List (Limited)',
      method: 'GET'
    },
    {
      url: `${BASE_URL}/api/customer/orders`,
      description: 'GET Customer Orders',
      method: 'GET'
    }
  ]
  
  const results = []
  
  for (const endpoint of endpoints) {
    const result = await checkEndpoint(endpoint.url, endpoint.description)
    results.push({ ...endpoint, ...result })
  }
  
  return results
}

async function testSpecificOrderOperations() {
  console.log('\n🔧 Testing Specific Order Operations...')
  
  // First, try to get any existing order
  const listResult = await checkEndpoint(
    `${BASE_URL}/api/e-commerce/orders?limit=1`,
    'Get First Order'
  )
  
  if (listResult.success && listResult.data?.data?.data?.length > 0) {
    const orderId = listResult.data.data.data[0].id
    console.log(`\n📝 Found existing order ID: ${orderId}`)
    
    // Test GET specific order (with admin header)
    await checkEndpoint(
      `${BASE_URL}/api/e-commerce/orders/${orderId}`,
      `GET Order ${orderId}`,
      { headers: { 'x-admin-request': 'true' } }
    )
    
    // Test order status update
    try {
      console.log(`\n🔄 Testing order status update on order ${orderId}...`)
      const updateResponse = await fetch(`${BASE_URL}/api/e-commerce/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: listResult.data.data.data[0].status, // Keep same status
          notifyCustomer: false
        })
      })
      
      console.log(`   Status Update Response: ${updateResponse.status}`)
      const updateData = await updateResponse.json()
      console.log(`   Status Update Data: ${JSON.stringify(updateData, null, 2)}`)
      
    } catch (error) {
      console.log(`   ❌ Status update test failed: ${error.message}`)
    }
    
    // Test general order update
    try {
      console.log(`\n🔄 Testing general order update on order ${orderId}...`)
      const updateResponse = await fetch(`${BASE_URL}/api/e-commerce/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true'
        },
        body: JSON.stringify({
          customerNote: "Test update from API health check",
          isAdmin: true
        })
      })
      
      console.log(`   General Update Response: ${updateResponse.status}`)
      const updateData = await updateResponse.json()
      console.log(`   General Update Data: ${JSON.stringify(updateData, null, 2)}`)
      
    } catch (error) {
      console.log(`   ❌ General update test failed: ${error.message}`)
    }
    
  } else {
    console.log('ℹ️  No existing orders found to test with')
  }
}

async function testBulkOperations() {
  console.log('\n🔄 Testing Bulk Order Operations...')
  
  // Test bulk operations endpoint
  await checkEndpoint(
    `${BASE_URL}/api/e-commerce/orders/bulk`,
    'Bulk Operations Endpoint (OPTIONS)',
    { method: 'OPTIONS' }
  )
}

async function runOrderHealthChecks() {
  console.log('🏥 Starting Order API Health Checks...')
  console.log('=' .repeat(60))
  
  // Check if server is running
  console.log('\n🌐 Checking Server Status...')
  const serverCheck = await checkEndpoint(BASE_URL, 'Server Root')
  
  if (!serverCheck.success) {
    console.log('❌ Server appears to be down or unreachable')
    console.log('💡 Make sure the development server is running with: npm run dev')
    return
  }
  
  console.log('✅ Server is responding')
  
  // Test order API endpoints
  const endpointResults = await testOrderEndpoints()
  
  // Test specific operations if orders exist
  await testSpecificOrderOperations()
  
  // Test bulk operations
  await testBulkOperations()
  
  // Summary
  console.log('\n' + '=' .repeat(60))
  console.log('📊 Order API Health Check Summary:')
  console.log(`🌐 Server: ${serverCheck.success ? '✅ OK' : '❌ FAIL'}`)
  
  const workingEndpoints = endpointResults.filter(r => r.success).length
  const totalEndpoints = endpointResults.length
  console.log(`🔗 Order API Endpoints: ${workingEndpoints}/${totalEndpoints} working`)
  
  if (workingEndpoints < totalEndpoints) {
    console.log('\n🔧 Some Order API endpoints are not working properly')
    console.log('   Check the server logs for detailed error messages')
    
    console.log('\n💡 Troubleshooting Tips:')
    console.log('   1. Check if your database server is running')
    console.log('   2. Verify DATABASE_URL in your .env file')
    console.log('   3. Run: npx prisma db push')
    console.log('   4. Check order service implementation')
    console.log('   5. Verify order API routes are properly configured')
  }
  
  if (workingEndpoints === totalEndpoints) {
    console.log('\n✅ All Order API endpoints are working correctly!')
  }
}

// Run health checks
runOrderHealthChecks().catch(console.error)
