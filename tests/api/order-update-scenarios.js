#!/usr/bin/env node

/**
 * Focused test script for Order Update functionality
 * Tests various update scenarios that might be failing in the admin
 */

const BASE_URL = 'http://localhost:3090'

async function makeRequest(url, options = {}) {
  try {
    console.log(`\n🔄 ${options.method || 'GET'} ${url}`)
    if (options.body) {
      console.log(`📤 Request Body:`, JSON.stringify(JSON.parse(options.body), null, 2))
    }

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'x-admin-request': 'true', // Add admin header for all requests
        ...options.headers
      },
      ...options
    })

    const data = await response.json()
    
    console.log(`📊 Status: ${response.status}`)
    console.log(`📋 Response:`, JSON.stringify(data, null, 2))
    
    return { response, data, status: response.status }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { error }
  }
}

// Get an existing order to test with
async function getTestOrder() {
  const { response, data } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders?limit=1`
  )

  if (response?.ok && data?.success) {
    const orders = data.data?.data || data.data
    if (orders && orders.length > 0) {
      console.log('✅ Found existing order for testing!')
      return orders[0]
    }
  }
  
  console.error('❌ No existing orders found to test with')
  return null
}

// Test different update scenarios
async function testBasicUpdate(orderId) {
  console.log('\n🧪 Test 1: Basic field updates')
  
  const updateData = {
    customerNote: "Updated customer note from API test",
    internalNotes: ["Updated internal notes", "Added via API test"],
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testStatusUpdate(orderId, currentStatus) {
  console.log('\n🧪 Test 2: Status updates')
  
  // Cycle through different statuses
  const statusCycle = {
    'pending': 'confirmed',
    'confirmed': 'processing',
    'processing': 'shipped',
    'shipped': 'delivered',
    'delivered': 'pending',
    'cancelled': 'pending'
  }
  
  const newStatus = statusCycle[currentStatus] || 'confirmed'
  
  const updateData = {
    status: newStatus,
    reason: "Status updated by API test",
    notifyCustomer: false,
    internalNote: "Test status update"
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}/status`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testPaymentStatusUpdate(orderId) {
  console.log('\n🧪 Test 3: Payment status updates')
  
  const updateData = {
    paymentStatus: 'paid',
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testFulfillmentStatusUpdate(orderId) {
  console.log('\n🧪 Test 4: Fulfillment status updates')
  
  const updateData = {
    fulfillmentStatus: 'fulfilled',
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testTagsUpdate(orderId) {
  console.log('\n🧪 Test 5: Tags updates')
  
  const updateData = {
    tags: ["urgent", "vip", "api-test"],
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testAttributesUpdate(orderId) {
  console.log('\n🧪 Test 6: Attributes updates')
  
  const updateData = {
    attributes: {
      "gift_message": "Happy Birthday!",
      "special_instructions": "Handle with care",
      "priority": "high"
    },
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testCompleteUpdate(orderId) {
  console.log('\n🧪 Test 7: Complete order update (like admin form)')
  
  const updateData = {
    customerNote: "Completely updated customer note",
    internalNotes: ["Completely updated internal notes", "Second note", "Third note"],
    tags: ["complete", "update", "test"],
    attributes: {
      "updated_by": "api_test",
      "update_timestamp": new Date().toISOString(),
      "test_complete": true
    },
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testInvalidUpdate(orderId) {
  console.log('\n🧪 Test 8: Invalid update (should fail)')
  
  const updateData = {
    status: "invalid_status", // Invalid status
    customerNote: "", // Empty note should be allowed
    isAdmin: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )
}

async function testBulkStatusUpdate() {
  console.log('\n🧪 Test 9: Bulk status update')
  
  // First get some order IDs
  const listResult = await makeRequest(`${BASE_URL}/api/e-commerce/orders?limit=3`)
  
  if (listResult.response?.ok && listResult.data?.success) {
    const orders = listResult.data.data?.data || listResult.data.data
    if (orders && orders.length > 0) {
      const orderIds = orders.map(order => order.id)
      
      const updateData = {
        orderIds: orderIds,
        action: "updateStatus",
        data: {
          status: "confirmed",
          reason: "Bulk update test"
        }
      }

      return await makeRequest(
        `${BASE_URL}/api/e-commerce/orders/bulk`,
        {
          method: 'POST',
          body: JSON.stringify(updateData)
        }
      )
    }
  }
  
  console.log('⚠️  No orders available for bulk update test')
  return { success: false, message: 'No orders available' }
}

async function verifyOrder(orderId) {
  console.log('\n🔍 Verifying current order state...')
  
  const { response, data } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`
  )

  if (response?.ok && data?.success) {
    const order = data.data
    console.log('📊 Current Order State:')
    console.log(`  Order Number: ${order.orderNumber}`)
    console.log(`  Status: ${order.status}`)
    console.log(`  Payment Status: ${order.paymentStatus}`)
    console.log(`  Customer: ${order.customerEmail}`)
    console.log(`  Total: ${order.total?.amount || 'N/A'} ${order.total?.currency || ''}`)
    console.log(`  Customer Note: ${order.customerNote || 'None'}`)
    console.log(`  Tags: ${order.tags?.join(', ') || 'None'}`)
    return order
  }
  return null
}

async function runUpdateTests() {
  console.log('🚀 Starting Order Update Tests...')
  console.log('=' .repeat(60))

  // Get test order
  const order = await getTestOrder()
  if (!order) {
    console.error('❌ Cannot run tests - no orders available')
    return
  }

  const orderId = order.id
  console.log(`\n📝 Test Order ID: ${orderId}`)
  console.log(`📝 Current Status: ${order.status}`)

  // Run update tests
  const tests = [
    () => testBasicUpdate(orderId),
    () => testStatusUpdate(orderId, order.status),
    () => testPaymentStatusUpdate(orderId),
    () => testFulfillmentStatusUpdate(orderId),
    () => testTagsUpdate(orderId),
    () => testAttributesUpdate(orderId),
    () => testCompleteUpdate(orderId),
    () => testInvalidUpdate(orderId),
    () => testBulkStatusUpdate()
  ]

  let passedTests = 0
  let failedTests = 0

  for (let i = 0; i < tests.length; i++) {
    try {
      const result = await tests[i]()
      
      if (result.error) {
        console.log(`❌ Test ${i + 1} failed with network error`)
        failedTests++
      } else if (result.status >= 200 && result.status < 300) {
        console.log(`✅ Test ${i + 1} passed`)
        passedTests++
        
        // Verify the update took effect (except for invalid test and bulk test)
        if (i < tests.length - 2) {
          await verifyOrder(orderId)
        }
      } else {
        console.log(`❌ Test ${i + 1} failed with status ${result.status}`)
        failedTests++
      }
    } catch (error) {
      console.error(`❌ Test ${i + 1} threw exception:`, error.message)
      failedTests++
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // Final verification
  await verifyOrder(orderId)

  console.log('\n' + '=' .repeat(60))
  console.log('🏁 Update Tests Summary:')
  console.log(`✅ Passed: ${passedTests}`)
  console.log(`❌ Failed: ${failedTests}`)
  console.log(`📊 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`)
}

// Run the tests
runUpdateTests().catch(console.error)
