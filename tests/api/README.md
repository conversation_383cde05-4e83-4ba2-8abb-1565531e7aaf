# API Test Scripts

This directory contains test scripts for testing the e-commerce API endpoints, specifically focusing on product management functionality.

## Scripts

### Product API Tests

#### 1. `health-check.js`
**Purpose**: Basic health check for the API and database connectivity
**Usage**: `node health-check.js`

Tests:
- Server connectivity
- Database connection
- Basic API endpoint availability
- Simple product operations

#### 2. `product-crud.js`
**Purpose**: Complete CRUD (Create, Read, Update, Delete) testing for products
**Usage**: `node product-crud.js`

Tests:
- Product creation
- Product retrieval
- Product updates
- Product deletion
- Product listing

#### 3. `product-update-scenarios.js`
**Purpose**: Focused testing of various product update scenarios
**Usage**: `node product-update-scenarios.js`

Tests:
- Basic field updates
- Price updates
- Inventory updates
- Status updates
- SEO updates
- Complete product updates
- Invalid update handling

### Order API Tests

#### 4. `order-health-check.js`
**Purpose**: Basic health check for the Order API functionality
**Usage**: `node order-health-check.js`

Tests:
- Order API endpoint availability
- Order retrieval operations
- Order status update operations
- Bulk operations endpoint

#### 5. `order-crud.js`
**Purpose**: Complete CRUD testing for orders
**Usage**: `node order-crud.js`

Tests:
- Order creation (with fallback to existing orders)
- Order retrieval
- Order updates
- Order status updates
- Customer orders
- Order listing

#### 6. `order-update-scenarios.js`
**Purpose**: Focused testing of various order update scenarios
**Usage**: `node order-update-scenarios.js`

Tests:
- Basic field updates
- Status updates
- Payment status updates
- Fulfillment status updates
- Tags updates
- Attributes updates
- Complete order updates
- Bulk status updates
- Invalid update handling

## Prerequisites

1. **Development server must be running**:
   ```bash
   npm run dev
   ```

2. **Database must be accessible**:
   - Check your `.env` file for correct `DATABASE_URL`
   - Ensure database server is running
   - Run `npx prisma db push` if needed

## Running Tests

### Individual Tests
```bash
# Run health check
node tests/api/health-check.js

# Run CRUD tests
node tests/api/product-crud.js

# Run update scenario tests
node tests/api/product-update-scenarios.js
```

### All Tests
```bash
# Run all tests in sequence
node tests/api/run-all.js
```

## Understanding Test Output

### Success Indicators
- ✅ Green checkmarks indicate successful operations
- 📊 Status codes 200-299 indicate success
- 🔍 Detailed response data is shown for debugging

### Failure Indicators
- ❌ Red X marks indicate failures
- 📋 Error messages and response data are displayed
- 🔧 Troubleshooting tips are provided

### Common Issues

1. **Server not running**:
   ```
   ❌ Server appears to be down or unreachable
   💡 Make sure the development server is running with: npm run dev
   ```

2. **Database connection issues**:
   ```
   ❌ Database connection may have issues
   💡 Check DATABASE_URL in .env file
   ```

3. **API endpoint failures**:
   - Check server logs for detailed error messages
   - Verify API route implementations
   - Check for validation errors

## Test Data

The scripts use test data that:
- Won't interfere with existing products
- Is automatically cleaned up after tests
- Uses realistic product information
- Tests edge cases and validation

## Debugging

If tests fail:

1. **Check the server logs** in your development terminal
2. **Verify database connectivity** using the health check
3. **Run tests individually** to isolate issues
4. **Check API responses** for detailed error messages

## Customization

You can modify the test scripts to:
- Change the `BASE_URL` for different environments
- Adjust test data to match your product schema
- Add additional test scenarios
- Modify timeout and retry logic

## Integration with CI/CD

These scripts can be integrated into your CI/CD pipeline:

```bash
# In your CI script
npm run dev &
sleep 10  # Wait for server to start
node tests/api/health-check.js
node tests/api/product-crud.js
```
