#!/usr/bin/env node

/**
 * Test script for Order CRUD Operations
 * This script tests the order Create, Read, Update operations
 */

const BASE_URL = 'http://localhost:3090'

// Test data for creating an order
const testOrderData = {
  customerEmail: "<EMAIL>",
  customerFirstName: "Test",
  customerLastName: "Customer",
  customerPhone: "+27123456789",
  shippingAddress: {
    firstName: "Test",
    lastName: "Customer",
    company: "Test Company",
    address1: "123 Test Street",
    address2: "Apt 4B",
    city: "Cape Town",
    province: "Western Cape",
    country: "South Africa",
    postalCode: "8001",
    phone: "+27123456789"
  },
  billingAddress: {
    firstName: "Test",
    lastName: "Customer",
    company: "Test Company",
    address1: "123 Test Street",
    address2: "Apt 4B",
    city: "Cape Town",
    province: "Western Cape",
    country: "South Africa",
    postalCode: "8001",
    phone: "+27123456789"
  },
  items: [
    {
      productId: "test-product-id",
      variantId: null,
      quantity: 2,
      unitPrice: { amount: 299.99, currency: "ZAR" }
    }
  ],
  shippingMethodId: "standard",
  paymentMethodId: "card",
  customerNote: "This is a test order created by the API test script"
}

// Updated test data for updating the order
const updateOrderData = {
  customerNote: "This order has been updated via API test",
  internalNotes: "Updated by API test script",
  tags: ["test", "api", "updated"],
  isAdmin: true
}

async function makeRequest(url, options = {}) {
  try {
    console.log(`\n🔄 Making ${options.method || 'GET'} request to: ${url}`)
    if (options.body) {
      console.log(`📤 Request Body:`, JSON.stringify(JSON.parse(options.body), null, 2))
    }

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'x-admin-request': 'true', // Add admin header for all requests
        ...options.headers
      },
      ...options
    })

    const data = await response.json()
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log(`📋 Response Data:`, JSON.stringify(data, null, 2))
    
    return { response, data }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { error }
  }
}

async function testCreateOrder() {
  console.log('\n🧪 Testing Order Creation...')
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders`,
    {
      method: 'POST',
      body: JSON.stringify(testOrderData)
    }
  )

  if (error) {
    console.error('❌ Create order failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Order created successfully!')
    return data.data
  } else {
    console.error('❌ Create order failed:', data.error || 'Unknown error')
    return null
  }
}

async function testGetOrder(orderId) {
  console.log(`\n🧪 Testing Get Order (ID: ${orderId})...`)
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`
  )

  if (error) {
    console.error('❌ Get order failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Order retrieved successfully!')
    return data.data
  } else {
    console.error('❌ Get order failed:', data.error || 'Unknown error')
    return null
  }
}

async function testUpdateOrder(orderId) {
  console.log(`\n🧪 Testing Order Update (ID: ${orderId})...`)
  
  const updateData = {
    ...updateOrderData,
    id: orderId
  }
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}`,
    {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    }
  )

  if (error) {
    console.error('❌ Update order failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Order updated successfully!')
    return data.data
  } else {
    console.error('❌ Update order failed:', data.error || 'Unknown error')
    console.error('📝 Full response:', data)
    return null
  }
}

async function testUpdateOrderStatus(orderId, currentStatus) {
  console.log(`\n🧪 Testing Order Status Update (ID: ${orderId})...`)
  
  // Test status update to a different status
  const newStatus = currentStatus === 'pending' ? 'confirmed' : 'pending'
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders/${orderId}/status`,
    {
      method: 'PATCH',
      body: JSON.stringify({
        status: newStatus,
        reason: "Status updated by API test",
        notifyCustomer: false,
        internalNote: "Test status update"
      })
    }
  )

  if (error) {
    console.error('❌ Update order status failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Order status updated successfully!')
    return data.data
  } else {
    console.error('❌ Update order status failed:', data.error || 'Unknown error')
    return null
  }
}

async function testListOrders() {
  console.log('\n🧪 Testing Order List...')
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/orders?limit=5`
  )

  if (error) {
    console.error('❌ List orders failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Orders listed successfully!')
    const orders = data.data?.data || data.data
    console.log(`📊 Found ${orders?.length || 0} orders`)
    return orders
  } else {
    console.error('❌ List orders failed:', data.error || 'Unknown error')
    return null
  }
}

async function testCustomerOrders() {
  console.log('\n🧪 Testing Customer Orders...')
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/customer/orders?limit=5`
  )

  if (error) {
    console.error('❌ Get customer orders failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Customer orders retrieved successfully!')
    console.log(`📊 Found ${data.orders?.length || 0} customer orders`)
    return data.orders
  } else {
    console.error('❌ Get customer orders failed:', data.error || 'Unknown error')
    return null
  }
}

async function runTests() {
  console.log('🚀 Starting Order CRUD Tests...')
  console.log('=' .repeat(50))

  // Test 1: List existing orders
  await testListOrders()

  // Test 2: Get customer orders
  await testCustomerOrders()

  // Test 3: Create a new order
  console.log('\n⚠️  Note: Order creation may fail if test product IDs don\'t exist')
  const createdOrder = await testCreateOrder()
  
  if (!createdOrder) {
    console.log('⚠️  Order creation failed - this is expected if test products don\'t exist')
    console.log('📝 Testing with existing orders instead...')
    
    // Get an existing order to test updates
    const orders = await testListOrders()
    if (orders && orders.length > 0) {
      const existingOrder = orders[0]
      console.log(`\n📝 Using existing order ID: ${existingOrder.id}`)
      
      // Test 4: Get the existing order
      await testGetOrder(existingOrder.id)
      
      // Test 5: Update the order
      await testUpdateOrder(existingOrder.id)
      
      // Test 6: Update order status
      await testUpdateOrderStatus(existingOrder.id, existingOrder.status)
      
      // Test 7: Get the updated order to verify changes
      console.log('\n🔍 Verifying updates by fetching order again...')
      const verifyOrder = await testGetOrder(existingOrder.id)
      
      if (verifyOrder) {
        console.log('\n📊 Verification Results:')
        console.log(`Order Number: ${verifyOrder.orderNumber}`)
        console.log(`Status: ${verifyOrder.status}`)
        console.log(`Customer: ${verifyOrder.customerEmail}`)
        console.log(`Total: ${verifyOrder.total?.amount || 'N/A'} ${verifyOrder.total?.currency || ''}`)
      }
    } else {
      console.log('❌ No existing orders found to test with')
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log('🏁 Order CRUD Tests Completed!')
    return
  }

  const orderId = createdOrder.id
  console.log(`\n📝 Created order ID: ${orderId}`)

  // Test 4: Get the created order
  await testGetOrder(orderId)

  // Test 5: Update the order
  const updatedOrder = await testUpdateOrder(orderId)

  // Test 6: Update order status
  if (updatedOrder) {
    await testUpdateOrderStatus(orderId, updatedOrder.status)
  }

  // Test 7: Get the updated order to verify changes
  console.log('\n🔍 Verifying updates by fetching order again...')
  const verifyOrder = await testGetOrder(orderId)
  
  if (verifyOrder) {
    console.log('\n📊 Verification Results:')
    console.log(`Order Number: ${verifyOrder.orderNumber}`)
    console.log(`Status: ${verifyOrder.status}`)
    console.log(`Customer: ${verifyOrder.customerEmail}`)
    console.log(`Total: ${verifyOrder.total?.amount || 'N/A'} ${verifyOrder.total?.currency || ''}`)
  }

  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Order CRUD Tests Completed!')
}

// Run the tests
runTests().catch(console.error)
