#!/usr/bin/env node

/**
 * API Health Check Script
 * Tests basic connectivity and database status
 */

const BASE_URL = 'http://localhost:3090'

async function checkEndpoint(url, description) {
  try {
    console.log(`\n🔍 Checking ${description}...`)
    console.log(`   URL: ${url}`)
    
    const startTime = Date.now()
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    const endTime = Date.now()
    
    const responseTime = endTime - startTime
    console.log(`   Status: ${response.status}`)
    console.log(`   Response Time: ${responseTime}ms`)
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      try {
        const data = await response.json()
        console.log(`   Response: ${JSON.stringify(data, null, 2)}`)
        return { success: response.ok, data, status: response.status, responseTime }
      } catch (e) {
        console.log(`   Response: [JSON Parse Error]`)
        return { success: false, error: 'Invalid JSON', status: response.status, responseTime }
      }
    } else {
      const text = await response.text()
      console.log(`   Response: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}`)
      return { success: response.ok, data: text, status: response.status, responseTime }
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function testDatabaseConnection() {
  console.log('\n🗄️  Testing Database Connection...')
  
  // Try to list products (this will test DB connection)
  const result = await checkEndpoint(
    `${BASE_URL}/api/e-commerce/products?limit=1`,
    'Product List (Database Test)'
  )
  
  if (result.success) {
    console.log('✅ Database connection appears to be working')
    return true
  } else {
    console.log('❌ Database connection may have issues')
    return false
  }
}

async function testProductEndpoints() {
  console.log('\n📦 Testing Product API Endpoints...')
  
  const endpoints = [
    {
      url: `${BASE_URL}/api/e-commerce/products`,
      description: 'GET Products List',
      method: 'GET'
    },
    {
      url: `${BASE_URL}/api/e-commerce/categories`,
      description: 'GET Categories List',
      method: 'GET'
    },
    {
      url: `${BASE_URL}/api/e-commerce/collections`,
      description: 'GET Collections List',
      method: 'GET'
    }
  ]
  
  const results = []
  
  for (const endpoint of endpoints) {
    const result = await checkEndpoint(endpoint.url, endpoint.description)
    results.push({ ...endpoint, ...result })
  }
  
  return results
}

async function testSpecificProductOperations() {
  console.log('\n🔧 Testing Specific Product Operations...')
  
  // First, try to get any existing product
  const listResult = await checkEndpoint(
    `${BASE_URL}/api/e-commerce/products?limit=1`,
    'Get First Product'
  )
  
  if (listResult.success && listResult.data?.data?.length > 0) {
    const productId = listResult.data.data[0].id
    console.log(`\n📝 Found existing product ID: ${productId}`)
    
    // Test GET specific product
    await checkEndpoint(
      `${BASE_URL}/api/e-commerce/products/${productId}`,
      `GET Product ${productId}`
    )
    
    // Test a minimal update (just to see if the endpoint responds)
    try {
      console.log(`\n🔄 Testing minimal update on product ${productId}...`)
      const updateResponse = await fetch(`${BASE_URL}/api/e-commerce/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: productId,
          title: listResult.data.data[0].title // Keep same title
        })
      })
      
      console.log(`   Update Status: ${updateResponse.status}`)
      const updateData = await updateResponse.json()
      console.log(`   Update Response: ${JSON.stringify(updateData, null, 2)}`)
      
    } catch (error) {
      console.log(`   ❌ Update test failed: ${error.message}`)
    }
    
  } else {
    console.log('ℹ️  No existing products found to test with')
  }
}

async function runHealthChecks() {
  console.log('🏥 Starting API Health Checks...')
  console.log('=' .repeat(60))
  
  // Check if server is running
  console.log('\n🌐 Checking Server Status...')
  const serverCheck = await checkEndpoint(BASE_URL, 'Server Root')
  
  if (!serverCheck.success) {
    console.log('❌ Server appears to be down or unreachable')
    console.log('💡 Make sure the development server is running with: npm run dev')
    return
  }
  
  console.log('✅ Server is responding')
  
  // Test database connection
  const dbStatus = await testDatabaseConnection()
  
  // Test API endpoints
  const endpointResults = await testProductEndpoints()
  
  // Test specific operations if DB is working
  if (dbStatus) {
    await testSpecificProductOperations()
  }
  
  // Summary
  console.log('\n' + '=' .repeat(60))
  console.log('📊 Health Check Summary:')
  console.log(`🌐 Server: ${serverCheck.success ? '✅ OK' : '❌ FAIL'}`)
  console.log(`🗄️  Database: ${dbStatus ? '✅ OK' : '❌ FAIL'}`)
  
  const workingEndpoints = endpointResults.filter(r => r.success).length
  const totalEndpoints = endpointResults.length
  console.log(`🔗 API Endpoints: ${workingEndpoints}/${totalEndpoints} working`)
  
  if (!dbStatus) {
    console.log('\n💡 Troubleshooting Tips:')
    console.log('   1. Check if your database server is running')
    console.log('   2. Verify DATABASE_URL in your .env file')
    console.log('   3. Run: npx prisma db push')
    console.log('   4. Check database connection settings')
  }
  
  if (workingEndpoints < totalEndpoints) {
    console.log('\n🔧 Some API endpoints are not working properly')
    console.log('   Check the server logs for detailed error messages')
  }
}

// Run health checks
runHealthChecks().catch(console.error)
