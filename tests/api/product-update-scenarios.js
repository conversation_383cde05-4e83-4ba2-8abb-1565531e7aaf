#!/usr/bin/env node

/**
 * Focused test script for Product Update functionality
 * Tests various update scenarios that might be failing in the admin
 */

const BASE_URL = 'http://localhost:3090'

async function makeRequest(url, options = {}) {
  try {
    console.log(`\n🔄 ${options.method || 'GET'} ${url}`)
    if (options.body) {
      console.log(`📤 Request Body:`, JSON.stringify(JSON.parse(options.body), null, 2))
    }
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })

    const data = await response.json()
    
    console.log(`📊 Status: ${response.status}`)
    console.log(`📋 Response:`, JSON.stringify(data, null, 2))
    
    return { response, data, status: response.status }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { error }
  }
}

// Create a test product first
async function createTestProduct() {
  const productData = {
    title: "Update Test Product",
    description: "Product created for update testing",
    price: 199.99, // Send as number, not object
    currency: 'ZAR',
    vendor: "Test Vendor",
    productType: "Test",
    status: "draft",
    trackQuantity: true,
    inventoryQuantity: 50,
    weight: 0.3,
    weightUnit: "kg",
    requiresShipping: true,
    isTaxable: true,
    isVisible: true
  }

  const { response, data } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products`,
    {
      method: 'POST',
      body: JSON.stringify(productData)
    }
  )

  if (response?.ok && data?.success) {
    console.log('✅ Test product created successfully!')
    return data.data
  } else {
    console.error('❌ Failed to create test product')
    return null
  }
}

// Test different update scenarios
async function testBasicUpdate(productId) {
  console.log('\n🧪 Test 1: Basic field updates')
  
  const updateData = {
    id: productId,
    title: "Updated Product Title",
    description: "Updated description",
    vendor: "Updated Vendor"
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testPriceUpdate(productId) {
  console.log('\n🧪 Test 2: Price updates')

  const updateData = {
    id: productId,
    price: 299.99, // Send as number for API route
    compareAtPrice: 399.99,
    costPerItem: 150.00,
    currency: 'ZAR'
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testInventoryUpdate(productId) {
  console.log('\n🧪 Test 3: Inventory updates')
  
  const updateData = {
    id: productId,
    inventoryQuantity: 75,
    trackQuantity: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testStatusUpdate(productId) {
  console.log('\n🧪 Test 4: Status updates')
  
  const updateData = {
    id: productId,
    status: "active",
    isVisible: true
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testSEOUpdate(productId) {
  console.log('\n🧪 Test 5: SEO updates')
  
  const updateData = {
    id: productId,
    seo: {
      title: "Updated SEO Title",
      description: "Updated SEO description",
      keywords: ["updated", "seo", "test"]
    }
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testCompleteUpdate(productId) {
  console.log('\n🧪 Test 6: Complete product update (like admin form)')

  const updateData = {
    id: productId,
    title: "Completely Updated Product",
    description: "This product has been completely updated",
    price: 449.99, // Send as number for API route
    compareAtPrice: 599.99,
    costPerItem: 200.00,
    currency: 'ZAR',
    vendor: "Complete Update Vendor",
    productType: "Updated Type",
    status: "active",
    trackQuantity: true,
    inventoryQuantity: 100,
    weight: 0.8,
    weightUnit: "kg",
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    seo: {
      title: "Complete Update SEO Title",
      description: "Complete update SEO description",
      keywords: ["complete", "update", "test"]
    },
    categoryIds: [],
    collectionIds: [],
    tags: ["updated", "complete"],
    images: []
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function testInvalidUpdate(productId) {
  console.log('\n🧪 Test 7: Invalid update (should fail)')

  const updateData = {
    id: productId,
    price: -100, // Invalid negative price
    title: "", // Empty title
    currency: 'ZAR'
  }

  return await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )
}

async function verifyProduct(productId) {
  console.log('\n🔍 Verifying current product state...')
  
  const { response, data } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`
  )

  if (response?.ok && data?.success) {
    const product = data.data
    console.log('📊 Current Product State:')
    console.log(`  Title: ${product.title}`)
    console.log(`  Price: R${product.price.amount}`)
    console.log(`  Status: ${product.status}`)
    console.log(`  Inventory: ${product.inventoryQuantity}`)
    console.log(`  Vendor: ${product.vendor}`)
    return product
  }
  return null
}

async function runUpdateTests() {
  console.log('🚀 Starting Product Update Tests...')
  console.log('=' .repeat(60))

  // Create test product
  const product = await createTestProduct()
  if (!product) {
    console.error('❌ Cannot run tests - failed to create test product')
    return
  }

  const productId = product.id
  console.log(`\n📝 Test Product ID: ${productId}`)

  // Run update tests
  const tests = [
    () => testBasicUpdate(productId),
    () => testPriceUpdate(productId),
    () => testInventoryUpdate(productId),
    () => testStatusUpdate(productId),
    () => testSEOUpdate(productId),
    () => testCompleteUpdate(productId),
    () => testInvalidUpdate(productId)
  ]

  let passedTests = 0
  let failedTests = 0

  for (let i = 0; i < tests.length; i++) {
    try {
      const result = await tests[i]()
      
      if (result.error) {
        console.log(`❌ Test ${i + 1} failed with network error`)
        failedTests++
      } else if (result.status >= 200 && result.status < 300) {
        console.log(`✅ Test ${i + 1} passed`)
        passedTests++
        
        // Verify the update took effect (except for invalid test)
        if (i < tests.length - 1) {
          await verifyProduct(productId)
        }
      } else {
        console.log(`❌ Test ${i + 1} failed with status ${result.status}`)
        failedTests++
      }
    } catch (error) {
      console.error(`❌ Test ${i + 1} threw exception:`, error.message)
      failedTests++
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // Final verification
  await verifyProduct(productId)

  // Cleanup
  console.log('\n🧹 Cleaning up test product...')
  await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    { method: 'DELETE' }
  )

  console.log('\n' + '=' .repeat(60))
  console.log('🏁 Update Tests Summary:')
  console.log(`✅ Passed: ${passedTests}`)
  console.log(`❌ Failed: ${failedTests}`)
  console.log(`📊 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`)
}

// Run the tests
runUpdateTests().catch(console.error)
