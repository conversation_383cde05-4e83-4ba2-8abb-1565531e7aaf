#!/usr/bin/env node

/**
 * Test script for Product APIs
 * This script tests the product CRUD operations by making direct API calls
 */

const BASE_URL = 'http://localhost:3090'

// Test data for creating a product
const testProductData = {
  title: "Test Product for API Testing",
  description: "This is a test product created by the API test script",
  price: { amount: 299.99, currency: 'ZAR' },
  compareAtPrice: { amount: 399.99, currency: 'ZAR' },
  costPerItem: { amount: 150.00, currency: 'ZAR' },
  vendor: "Test Vendor",
  productType: "Test Type",
  status: "draft",
  trackQuantity: true,
  inventoryQuantity: 100,
  weight: 0.5,
  weightUnit: "kg",
  requiresShipping: true,
  isTaxable: true,
  isVisible: true,
  seo: {
    title: "Test Product SEO Title",
    description: "Test product SEO description",
    keywords: ["test", "product", "api"]
  },
  categoryIds: [],
  collectionIds: [],
  tags: ["test", "api"],
  images: []
}

// Updated test data for updating the product
const updateProductData = {
  title: "Updated Test Product",
  description: "This product has been updated via API test",
  price: { amount: 349.99, currency: 'ZAR' },
  vendor: "Updated Vendor",
  status: "active",
  inventoryQuantity: 150
}

async function makeRequest(url, options = {}) {
  try {
    console.log(`\n🔄 Making ${options.method || 'GET'} request to: ${url}`)
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })

    const data = await response.json()
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log(`📋 Response Data:`, JSON.stringify(data, null, 2))
    
    return { response, data }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { error }
  }
}

async function testCreateProduct() {
  console.log('\n🧪 Testing Product Creation...')
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products`,
    {
      method: 'POST',
      body: JSON.stringify(testProductData)
    }
  )

  if (error) {
    console.error('❌ Create product failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Product created successfully!')
    return data.data
  } else {
    console.error('❌ Create product failed:', data.error || 'Unknown error')
    return null
  }
}

async function testGetProduct(productId) {
  console.log(`\n🧪 Testing Get Product (ID: ${productId})...`)
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`
  )

  if (error) {
    console.error('❌ Get product failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Product retrieved successfully!')
    return data.data
  } else {
    console.error('❌ Get product failed:', data.error || 'Unknown error')
    return null
  }
}

async function testUpdateProduct(productId) {
  console.log(`\n🧪 Testing Product Update (ID: ${productId})...`)
  
  const updateData = {
    ...updateProductData,
    id: productId
  }
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'PUT',
      body: JSON.stringify(updateData)
    }
  )

  if (error) {
    console.error('❌ Update product failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Product updated successfully!')
    return data.data
  } else {
    console.error('❌ Update product failed:', data.error || 'Unknown error')
    console.error('📝 Full response:', data)
    return null
  }
}

async function testDeleteProduct(productId) {
  console.log(`\n🧪 Testing Product Deletion (ID: ${productId})...`)
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products/${productId}`,
    {
      method: 'DELETE'
    }
  )

  if (error) {
    console.error('❌ Delete product failed:', error)
    return false
  }

  if (response.ok && data.success) {
    console.log('✅ Product deleted successfully!')
    return true
  } else {
    console.error('❌ Delete product failed:', data.error || 'Unknown error')
    return false
  }
}

async function testListProducts() {
  console.log('\n🧪 Testing Product List...')
  
  const { response, data, error } = await makeRequest(
    `${BASE_URL}/api/e-commerce/products?limit=5`
  )

  if (error) {
    console.error('❌ List products failed:', error)
    return null
  }

  if (response.ok && data.success) {
    console.log('✅ Products listed successfully!')
    console.log(`📊 Found ${data.data.length} products`)
    return data.data
  } else {
    console.error('❌ List products failed:', data.error || 'Unknown error')
    return null
  }
}

async function runTests() {
  console.log('🚀 Starting Product API Tests...')
  console.log('=' .repeat(50))

  // Test 1: List existing products
  await testListProducts()

  // Test 2: Create a new product
  const createdProduct = await testCreateProduct()
  if (!createdProduct) {
    console.error('❌ Cannot continue tests - product creation failed')
    return
  }

  const productId = createdProduct.id
  console.log(`\n📝 Created product ID: ${productId}`)

  // Test 3: Get the created product
  await testGetProduct(productId)

  // Test 4: Update the product
  const updatedProduct = await testUpdateProduct(productId)

  // Test 5: Get the updated product to verify changes
  if (updatedProduct) {
    console.log('\n🔍 Verifying update by fetching product again...')
    const verifyProduct = await testGetProduct(productId)
    
    if (verifyProduct) {
      console.log('\n📊 Verification Results:')
      console.log(`Title: ${verifyProduct.title}`)
      console.log(`Price: R${verifyProduct.price.amount}`)
      console.log(`Status: ${verifyProduct.status}`)
      console.log(`Inventory: ${verifyProduct.inventoryQuantity}`)
    }
  }

  // Test 6: Clean up - delete the test product
  console.log('\n🧹 Cleaning up test data...')
  await testDeleteProduct(productId)

  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Product API Tests Completed!')
}

// Run the tests
runTests().catch(console.error)
